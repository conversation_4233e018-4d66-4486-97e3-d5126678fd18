{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "CorsHosts": ["http://localhost:3000", "http://localhost:3001", "http://localhost:5000", "http://localhost:5001", "http://localhost:5002", "http://localhost:5003", "http://localhost:5004", "http://localhost:5005", "http://localhost:5006", "http://localhost:5007", "http://localhost:5008", "http://localhost:5009"], "AllowedHosts": "*", "JwtSecret": "acc6ee8e70bd7bdef6d11e8b800991dc7434b3571d248e3cb7ec5f543c955aed4ee0244b4355a430949c0d7070bdaa9cf6e65410f926eed9b7080f6972c40d63e798187cdd8d393b1e0979edd2392286da4be1a553c62e891819dbac399f1bcf86d6248b422841061664de32c7b351203fa059281eee2ef9cc3a31f7ee4ad262a5f970acfd2937ce70fe3194286947ade017e1ada9f7b70b919fb9de7d4f27673abbf0e99f19ae190dcc15a15688b5e37b572b366caea7f2ee83076572760bed4e85da5005a184082a08ad5f38e637ba1813e8245e402f7325c7dbdc150c9f592b3a59c72c33d57e42fa40fbd7a17289d8aeb4c39af92875101dfc07d3105767", "NextAuth": {"Secret": "acc6ee8e70bd7bdef6d11e8b800991dc7434b3571d248e3cb7ec5f543c955aed4ee0244b4355a430949c0d7070bdaa9cf6e65410f926eed9b7080f6972c40d63e798187cdd8d393b1e0979edd2392286da4be1a553c62e891819dbac399f1bcf86d6248b422841061664de32c7b351203fa059281eee2ef9cc3a31f7ee4ad262a5f970acfd2937ce70fe3194286947ade017e1ada9f7b70b919fb9de7d4f27673abbf0e99f19ae190dcc15a15688b5e37b572b366caea7f2ee83076572760bed4e85da5005a184082a08ad5f38e637ba1813e8245e402f7325c7dbdc150c9f592b3a59c72c33d57e42fa40fbd7a17289d8aeb4c39af92875101dfc07d3105767"}}