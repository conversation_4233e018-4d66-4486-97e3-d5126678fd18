{"Routes": [{"DownstreamPathTemplate": "/api/ProductImage/{imageId}/paths", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33801}], "UpstreamPathTemplate": "/internal-api/ProductImage/{imageId}/paths", "UpstreamHttpMethod": ["PATCH"], "Priority": 0}, {"DownstreamPathTemplate": "/api/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33801}], "UpstreamPathTemplate": "/admin-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "AuthenticationOptions": {"AuthenticationProviderKey": "J<PERSON>t<PERSON><PERSON><PERSON>", "AllowedScopes": []}, "Priority": 1}, {"DownstreamPathTemplate": "/api/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33804}], "UpstreamPathTemplate": "/web-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "Priority": 1, "EnableCors": true}, {"DownstreamPathTemplate": "/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33803}], "UpstreamPathTemplate": "/mail-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE"], "AuthenticationOptions": {"AuthenticationProviderKey": "J<PERSON>t<PERSON><PERSON><PERSON>", "AllowedScopes": []}}, {"DownstreamPathTemplate": "/api/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33802}], "UpstreamPathTemplate": "/media-api/{catchAll}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "AuthenticationOptions": {"AuthenticationProviderKey": "J<PERSON>t<PERSON><PERSON><PERSON>", "AllowedScopes": []}}, {"DownstreamPathTemplate": "/api/auth/login", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33801}], "UpstreamPathTemplate": "/admin/login", "UpstreamHttpMethod": ["POST"], "Priority": 0}, {"DownstreamPathTemplate": "/api/auth/register", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33801}], "UpstreamPathTemplate": "/admin/register", "UpstreamHttpMethod": ["POST"], "Priority": 0}, {"DownstreamPathTemplate": "/api/customer/register", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33804}], "UpstreamPathTemplate": "/customer/register", "UpstreamHttpMethod": ["POST"], "Priority": 0}, {"DownstreamPathTemplate": "/images/original/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33801}], "UpstreamPathTemplate": "/images/original/{catchAll}", "UpstreamHttpMethod": ["GET"], "Priority": 1}, {"DownstreamPathTemplate": "/images/webp/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33802}], "UpstreamPathTemplate": "/images/webp/{catchAll}", "UpstreamHttpMethod": ["GET"], "Priority": 1}, {"DownstreamPathTemplate": "/images/thumbnails/{catchAll}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 33802}], "UpstreamPathTemplate": "/images/thumbnails/{catchAll}", "UpstreamHttpMethod": ["GET"], "Priority": 1}], "GlobalConfiguration": {"BaseUrl": "http://localhost:33800"}}