using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowCustomerFrontend")]
public class ProductController : ControllerBase
{
    private readonly ICustomerProductService _customerProductService;

    public ProductController(ICustomerProductService customerProductService)
    {
        _customerProductService = customerProductService;
    }

    /// <summary>
    /// Unified ürün listesi API'si - /urunler, /kategoriler, /yeni-sezon, /indirim sayfaları için server-side rendering desteği
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<ProductPageDataDto>>> GetProducts([FromQuery] ProductFilterDto filter)
    {
        try
        {
            var result = await _customerProductService.GetProductPageDataAsync(filter);
            return Ok(ApiResponse<ProductPageDataDto>.SuccessResponse(result, "Ürünler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ProductPageDataDto>.ErrorResponse("Ürünler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Filter seçenekleri API'si - server-side rendering için kategoriler, markalar, boyutlar, fiyat aralığı
    /// </summary>
    [HttpGet("filters")]
    public async Task<ActionResult<ApiResponse<ProductFilterOptionsDto>>> GetFilters([FromQuery] string? pageType = null, [FromQuery] string? categorySlug = null)
    {
        try
        {
            var result = await _customerProductService.GetFilterOptionsAsync(pageType, categorySlug);
            return Ok(ApiResponse<ProductFilterOptionsDto>.SuccessResponse(result, "Filter seçenekleri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ProductFilterOptionsDto>.ErrorResponse("Filter seçenekleri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün detay API'si - slug parametresi ile tekil ürün getirme + ProductSEO metadata
    /// </summary>
    [HttpGet("{slug}")]
    public async Task<ActionResult<ApiResponse<ProductDetailPageDataDto>>> GetProductBySlug(string slug)
    {
        try
        {
            var result = await _customerProductService.GetProductDetailPageDataAsync(slug);
            if (result == null)
            {
                return NotFound(ApiResponse<ProductDetailPageDataDto>.NotFoundResponse("Ürün bulunamadı."));
            }
            return Ok(ApiResponse<ProductDetailPageDataDto>.SuccessResponse(result, "Ürün detayı başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ProductDetailPageDataDto>.ErrorResponse("Ürün detayı getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün yorumları API'si - belirli ürüne ait yorumları getirme
    /// </summary>
    [HttpGet("{id:guid}/reviews")]
    public async Task<ActionResult<PaginatedApiResponse<CustomerProductReviewDto>>> GetProductReviews(Guid id, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            var result = await _customerProductService.GetProductReviewsAsync(id, page, pageSize);
            var pagination = new PaginationDto
            {
                Page = page,
                PageSize = pageSize,
                TotalItems = result.Count,
                TotalPages = (int)Math.Ceiling((double)result.Count / pageSize)
            };
            return Ok(PaginatedApiResponse<CustomerProductReviewDto>.SuccessResponse(result, pagination, "Ürün yorumları başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, PaginatedApiResponse<CustomerProductReviewDto>.ErrorResponse("Ürün yorumları getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Öne çıkan ürünler API'si - ana sayfada gösterilecek ürünler
    /// </summary>
    [HttpGet("featured")]
    public async Task<ActionResult<ApiResponse<List<CustomerListProductDto>>>> GetFeaturedProducts([FromQuery] int count = 12)
    {
        try
        {
            var result = await _customerProductService.GetFeaturedProductsAsync(count);
            return Ok(ApiResponse<List<CustomerListProductDto>>.SuccessResponse(result, "Öne çıkan ürünler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerListProductDto>>.ErrorResponse("Öne çıkan ürünler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Önerilen ürünler API'si - kullanıcıya önerilen ürünler
    /// </summary>
    [HttpGet("recommended")]
    public async Task<ActionResult<ApiResponse<List<CustomerListProductDto>>>> GetRecommendedProducts([FromQuery] int count = 12)
    {
        try
        {
            var result = await _customerProductService.GetRecommendedProductsAsync(count);
            return Ok(ApiResponse<List<CustomerListProductDto>>.SuccessResponse(result, "Önerilen ürünler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerListProductDto>>.ErrorResponse("Önerilen ürünler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün arama API'si - metin tabanlı ürün arama işlevselliği
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<ProductPageDataDto>>> SearchProducts([FromQuery] ProductSearchRequestDto request)
    {
        try
        {
            var result = await _customerProductService.SearchProductsAsync(request);
            return Ok(ApiResponse<ProductPageDataDto>.SuccessResponse(result, "Ürün arama işlemi başarıyla tamamlandı"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ProductPageDataDto>.ErrorResponse("Ürün arama işlemi sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Sayfa türüne göre SEO metadata API'si - /urunler, /kategoriler, /yeni-sezon, /indirim sayfaları için dinamik metadata
    /// </summary>
    [HttpGet("page-metadata")]
    public async Task<ActionResult<ApiResponse<PageMetadataDto>>> GetPageMetadata([FromQuery] PageMetadataRequestDto request)
    {
        try
        {
            var result = await _customerProductService.GetPageMetadataAsync(request);
            return Ok(ApiResponse<PageMetadataDto>.SuccessResponse(result, "Sayfa metadata'sı başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<PageMetadataDto>.ErrorResponse("Sayfa metadata'sı getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Yeni sezon ürünleri API'si
    /// </summary>
    [HttpGet("new-season")]
    public async Task<ActionResult<ApiResponse<List<CustomerListProductDto>>>> GetNewSeasonProducts([FromQuery] int count = 12)
    {
        try
        {
            var result = await _customerProductService.GetNewSeasonProductsAsync(count);
            return Ok(ApiResponse<List<CustomerListProductDto>>.SuccessResponse(result, "Yeni sezon ürünleri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerListProductDto>>.ErrorResponse("Yeni sezon ürünleri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// İndirimli ürünler API'si
    /// </summary>
    [HttpGet("discounted")]
    public async Task<ActionResult<ApiResponse<List<CustomerListProductDto>>>> GetDiscountedProducts([FromQuery] int count = 12)
    {
        try
        {
            var result = await _customerProductService.GetDiscountedProductsAsync(count);
            return Ok(ApiResponse<List<CustomerListProductDto>>.SuccessResponse(result, "İndirimli ürünler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerListProductDto>>.ErrorResponse("İndirimli ürünler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ana sayfa verisi API'si
    /// </summary>
    [HttpGet("home-data")]
    public async Task<ActionResult<ApiResponse<HomePageDataDto>>> GetHomePageData()
    {
        try
        {
            var result = await _customerProductService.GetHomePageDataAsync();
            return Ok(ApiResponse<HomePageDataDto>.SuccessResponse(result, "Ana sayfa verisi başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<HomePageDataDto>.ErrorResponse("Ana sayfa verisi getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün stok durumu kontrolü
    /// </summary>
    [HttpGet("{id:guid}/stock")]
    public async Task<ActionResult<ApiResponse<object>>> GetProductStock(Guid id)
    {
        try
        {
            var inStock = await _customerProductService.IsProductInStockAsync(id);
            var quantity = await _customerProductService.GetProductStockQuantityAsync(id);
            var isActive = await _customerProductService.IsProductActiveAsync(id);

            var stockData = new { inStock, quantity, isActive };
            return Ok(ApiResponse<object>.SuccessResponse(stockData, "Ürün stok bilgisi başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResponse("Ürün stok bilgisi getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün varyantları (renkler)
    /// </summary>
    [HttpGet("{id:guid}/colors")]
    public async Task<ActionResult<ApiResponse<List<CustomerVariantOptionDto>>>> GetProductColors(Guid id)
    {
        try
        {
            var result = await _customerProductService.GetProductColorsAsync(id);
            return Ok(ApiResponse<List<CustomerVariantOptionDto>>.SuccessResponse(result, "Ürün renkleri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerVariantOptionDto>>.ErrorResponse("Ürün renkleri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün varyantları (boyutlar)
    /// </summary>
    [HttpGet("{id:guid}/sizes")]
    public async Task<ActionResult<ApiResponse<List<CustomerVariantOptionDto>>>> GetProductSizes(Guid id)
    {
        try
        {
            var result = await _customerProductService.GetProductSizesAsync(id);
            return Ok(ApiResponse<List<CustomerVariantOptionDto>>.SuccessResponse(result, "Ürün boyutları başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerVariantOptionDto>>.ErrorResponse("Ürün boyutları getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ürün görselleri
    /// </summary>
    [HttpGet("{id:guid}/images")]
    public async Task<ActionResult<ApiResponse<List<CustomerProductImageDto>>>> GetProductImages(Guid id)
    {
        try
        {
            var result = await _customerProductService.GetProductImagesAsync(id);
            return Ok(ApiResponse<List<CustomerProductImageDto>>.SuccessResponse(result, "Ürün görselleri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerProductImageDto>>.ErrorResponse("Ürün görselleri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// İlgili ürünler
    /// </summary>
    [HttpGet("{id:guid}/related")]
    public async Task<ActionResult<ApiResponse<List<CustomerListProductDto>>>> GetRelatedProducts(Guid id, [FromQuery] int count = 8)
    {
        try
        {
            var result = await _customerProductService.GetRelatedProductsAsync(id, count);
            return Ok(ApiResponse<List<CustomerListProductDto>>.SuccessResponse(result, "İlgili ürünler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerListProductDto>>.ErrorResponse("İlgili ürünler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}
