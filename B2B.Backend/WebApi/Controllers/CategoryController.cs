using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowCustomerFrontend")]
public class CategoryController : ControllerBase
{
    private readonly ICustomerCategoryService _customerCategoryService;

    public CategoryController(ICustomerCategoryService customerCategoryService)
    {
        _customerCategoryService = customerCategoryService;
    }

    /// <summary>
    /// Kategori listesi API'si - tüm kategorileri getirme
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetCategories()
    {
        try
        {
            var result = await _customerCategoryService.GetCategoriesAsync();
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Ana kategoriler API'si
    /// </summary>
    [HttpGet("main")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetMainCategories()
    {
        try
        {
            var result = await _customerCategoryService.GetMainCategoriesAsync();
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Ana kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Ana kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategori hiyerarşisi API'si
    /// </summary>
    [HttpGet("hierarchy")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetCategoryHierarchy()
    {
        try
        {
            var result = await _customerCategoryService.GetCategoryHierarchyAsync();
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Kategori hiyerarşisi başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Kategori hiyerarşisi getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategori detay API'si - slug parametresi ile tekil kategori getirme
    /// </summary>
    [HttpGet("{slug}")]
    public async Task<ActionResult<ApiResponse<CustomerCategoryDto>>> GetCategoryBySlug(string slug)
    {
        try
        {
            var result = await _customerCategoryService.GetCategoryBySlugAsync(slug);
            if (result == null)
            {
                return NotFound(ApiResponse<CustomerCategoryDto>.NotFoundResponse("Kategori bulunamadı."));
            }
            return Ok(ApiResponse<CustomerCategoryDto>.SuccessResponse(result, "Kategori detayı başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerCategoryDto>.ErrorResponse("Kategori detayı getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategoriye ait ürünler API'si - belirli kategorideki ürünleri getirme
    /// </summary>
    [HttpGet("{slug}/products")]
    public async Task<ActionResult<ApiResponse<CategoryPageDataDto>>> GetCategoryProducts(string slug, [FromQuery] ProductFilterDto filter)
    {
        try
        {
            var result = await _customerCategoryService.GetCategoryPageDataAsync(slug, filter);
            return Ok(ApiResponse<CategoryPageDataDto>.SuccessResponse(result, "Kategori ürünleri başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CategoryPageDataDto>.ErrorResponse("Kategori ürünleri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Alt kategoriler API'si
    /// </summary>
    [HttpGet("{parentId:guid}/subcategories")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetSubCategories(Guid parentId)
    {
        try
        {
            var result = await _customerCategoryService.GetSubCategoriesAsync(parentId);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Alt kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Alt kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategori breadcrumb API'si
    /// </summary>
    [HttpGet("{slug}/breadcrumb")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetCategoryBreadcrumb(string slug)
    {
        try
        {
            var result = await _customerCategoryService.GetCategoryBreadcrumbAsync(slug);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Kategori breadcrumb başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Kategori breadcrumb getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kardeş kategoriler API'si
    /// </summary>
    [HttpGet("{slug}/siblings")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetSiblingCategories(string slug)
    {
        try
        {
            var result = await _customerCategoryService.GetSiblingCategoriesAsync(slug);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Kardeş kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Kardeş kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Alt kategoriler API'si (slug ile)
    /// </summary>
    [HttpGet("{slug}/children")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetChildCategories(string slug)
    {
        try
        {
            var result = await _customerCategoryService.GetChildCategoriesAsync(slug);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Alt kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Alt kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategori SEO metadata API'si
    /// </summary>
    [HttpGet("{slug}/metadata")]
    public async Task<ActionResult<ApiResponse<CategoryPageMetadataDto>>> GetCategoryMetadata(string slug)
    {
        try
        {
            var result = await _customerCategoryService.GetCategoryMetadataAsync(slug);
            return Ok(ApiResponse<CategoryPageMetadataDto>.SuccessResponse(result, "Kategori metadata'sı başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CategoryPageMetadataDto>.ErrorResponse("Kategori metadata'sı getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Popüler kategoriler API'si
    /// </summary>
    [HttpGet("popular")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetPopularCategories([FromQuery] int count = 10)
    {
        try
        {
            var result = await _customerCategoryService.GetPopularCategoriesAsync(count);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Popüler kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Popüler kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Öne çıkan kategoriler API'si
    /// </summary>
    [HttpGet("featured")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> GetFeaturedCategories([FromQuery] int count = 6)
    {
        try
        {
            var result = await _customerCategoryService.GetFeaturedCategoriesAsync(count);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Öne çıkan kategoriler başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Öne çıkan kategoriler getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategori arama API'si
    /// </summary>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<List<CustomerCategoryDto>>>> SearchCategories([FromQuery] string query, [FromQuery] int count = 10)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(ApiResponse<List<CustomerCategoryDto>>.BadRequestResponse("Arama sorgusu boş olamaz."));
            }

            var result = await _customerCategoryService.SearchCategoriesAsync(query, count);
            return Ok(ApiResponse<List<CustomerCategoryDto>>.SuccessResponse(result, "Kategori arama işlemi başarıyla tamamlandı"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CustomerCategoryDto>>.ErrorResponse("Kategori arama işlemi sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Kategori durumu kontrolü
    /// </summary>
    [HttpGet("{slug}/status")]
    public async Task<ActionResult<ApiResponse<object>>> GetCategoryStatus(string slug)
    {
        try
        {
            var isActive = await _customerCategoryService.IsCategoryActiveAsync(slug);
            var exists = await _customerCategoryService.CategoryExistsAsync(slug);
            var productCount = await _customerCategoryService.GetCategoryProductCountAsync(slug);

            var statusData = new { isActive, exists, productCount };
            return Ok(ApiResponse<object>.SuccessResponse(statusData, "Kategori durumu başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResponse("Kategori durumu kontrol edilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}
