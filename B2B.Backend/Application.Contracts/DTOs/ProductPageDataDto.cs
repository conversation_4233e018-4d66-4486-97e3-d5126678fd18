namespace Application.Contracts.DTOs;

/// <summary>
/// B2C unified sayfa verisi için DTO - ürünler + metadata + filters + pagination
/// </summary>
public class ProductPageDataDto
{
    public List<CustomerListProductDto> Products { get; set; } = [];
    public ProductFilterOptionsDto FilterOptions { get; set; } = new();
    public PaginationDto Pagination { get; set; } = new();
    public PageMetadataDto Metadata { get; set; } = new();
    public string PageType { get; set; } = null!; // "all", "category", "new-season", "discount"
    public string? CategorySlug { get; set; }
    public int TotalProducts { get; set; }
}

/// <summary>
/// B2C ürün detay sayfası verisi için DTO
/// </summary>
public class ProductDetailPageDataDto
{
    public CustomerProductDto Product { get; set; } = new();
    public List<CustomerListProductDto> RecommendedProducts { get; set; } = [];
    public List<CustomerListProductDto> RelatedProducts { get; set; } = [];
    public ProductPageMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// B2C kategori sayfası verisi için DTO
/// </summary>
public class CategoryPageDataDto
{
    public CustomerCategoryDto Category { get; set; } = new();
    public List<CustomerListProductDto> Products { get; set; } = [];
    public ProductFilterOptionsDto FilterOptions { get; set; } = new();
    public PaginationDto Pagination { get; set; } = new();
    public CategoryPageMetadataDto Metadata { get; set; } = new();
    public int TotalProducts { get; set; }
}

/// <summary>
/// B2C ana sayfa verisi için DTO
/// </summary>
public class HomePageDataDto
{
    public List<CustomerListProductDto> FeaturedProducts { get; set; } = [];
    public List<CustomerListProductDto> NewProducts { get; set; } = [];
    public List<CustomerListProductDto> DiscountedProducts { get; set; } = [];
    public List<CustomerCategoryDto> FeaturedCategories { get; set; } = [];
    public PageMetadataDto Metadata { get; set; } = new();
}
