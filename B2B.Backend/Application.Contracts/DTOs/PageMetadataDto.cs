namespace Application.Contracts.DTOs;

/// <summary>
/// B2C sayfa türüne göre SEO metadata için DTO
/// </summary>
public class PageMetadataDto
{
    public string Title { get; set; } = null!;
    public string Description { get; set; } = null!;
    public string? Keywords { get; set; }
    public string? CanonicalUrl { get; set; }
    public string? OgTitle { get; set; }
    public string? OgDescription { get; set; }
    public string? OgImage { get; set; }
    public string? OgUrl { get; set; }
    public string? TwitterTitle { get; set; }
    public string? TwitterDescription { get; set; }
    public string? TwitterImage { get; set; }
    public string? JsonLd { get; set; } // Structured data
}

/// <summary>
/// B2C sayfa metadata request için DTO
/// </summary>
public class PageMetadataRequestDto
{
    public string PageType { get; set; } = null!; // "all", "category", "new-season", "discount", "product"
    public string? CategorySlug { get; set; }
    public string? ProductSlug { get; set; }
    public string? BaseUrl { get; set; }
}

/// <summary>
/// B2C ürün sayfa metadata için DTO
/// </summary>
public class ProductPageMetadataDto : PageMetadataDto
{
    public string ProductName { get; set; } = null!;
    public string? ProductDescription { get; set; }
    public decimal? ProductPrice { get; set; }
    public string? ProductImage { get; set; }
    public string? ProductBrand { get; set; }
    public string? ProductCategory { get; set; }
    public bool? ProductInStock { get; set; }
    public string? ProductSku { get; set; }
}

/// <summary>
/// B2C kategori sayfa metadata için DTO
/// </summary>
public class CategoryPageMetadataDto : PageMetadataDto
{
    public string CategoryName { get; set; } = null!;
    public string? CategoryDescription { get; set; }
    public string? CategoryImage { get; set; }
    public int ProductCount { get; set; }
}
