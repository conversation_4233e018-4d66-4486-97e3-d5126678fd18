namespace Application.Contracts.DTOs;

/// <summary>
/// B2C ürün filter seçenekleri için DTO - server-side rendering için
/// </summary>
public class ProductFilterOptionsDto
{
    public List<FilterCategoryDto> Categories { get; set; } = [];
    public List<FilterBrandDto> Brands { get; set; } = [];
    public List<FilterSizeDto> Sizes { get; set; } = [];
    public List<FilterVariantAttributeDto> VariantAttributes { get; set; } = [];
    public FilterPriceRangeDto PriceRange { get; set; } = new();
    public FilterAvailabilityDto Availability { get; set; } = new();
}

/// <summary>
/// Filter kategori seçeneği için DTO
/// </summary>
public class FilterCategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Slug { get; set; } = null!;
    public int ProductCount { get; set; }
}

/// <summary>
/// Filter marka seçeneği için DTO
/// </summary>
public class FilterBrandDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public int ProductCount { get; set; }
}

/// <summary>
/// Filter boyut seçeneği için DTO
/// </summary>
public class FilterSizeDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public int ProductCount { get; set; }
}

/// <summary>
/// Filter fiyat aralığı için DTO
/// </summary>
public class FilterPriceRangeDto
{
    public decimal MinPrice { get; set; }
    public decimal MaxPrice { get; set; }
}

/// <summary>
/// Filter stok durumu için DTO
/// </summary>
public class FilterAvailabilityDto
{
    public int InStockCount { get; set; }
    public int OutOfStockCount { get; set; }
    public int TotalCount { get; set; }
}

/// <summary>
/// Filter varyant niteliği için DTO
/// </summary>
public class FilterVariantAttributeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string ShortName { get; set; } = null!;
    public List<FilterVariantAttributeValueDto> Values { get; set; } = [];
}

/// <summary>
/// Filter varyant nitelik değeri için DTO
/// </summary>
public class FilterVariantAttributeValueDto
{
    public Guid Id { get; set; }
    public string Value { get; set; } = null!;
    public int ProductCount { get; set; }
}
