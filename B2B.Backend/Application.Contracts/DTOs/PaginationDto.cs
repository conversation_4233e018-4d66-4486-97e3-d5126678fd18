namespace Application.Contracts.DTOs;

/// <summary>
/// B2C sayfalama bilgileri için DTO
/// </summary>
public class PaginationDto
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 12;
    public int TotalItems { get; set; }
    public int TotalPages { get; set; }
    public bool HasPreviousPage { get; set; }
    public bool HasNextPage { get; set; }
    public int? PreviousPage { get; set; }
    public int? NextPage { get; set; }
}

/// <summary>
/// Generic sayfalama response DTO
/// </summary>
/// <typeparam name="T">Sayfalanacak veri tipi</typeparam>
public class PagedResultDto<T>
{
    public List<T> Items { get; set; } = [];
    public PaginationDto Pagination { get; set; } = new();
    public int TotalCount { get; set; }
}

/// <summary>
/// Sayfalama request parametreleri için DTO
/// </summary>
public class PaginationRequestDto
{
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 12;
    
    // Validation
    public void Validate()
    {
        if (Page < 1) Page = 1;
        if (PageSize < 1) PageSize = 12;
        if (PageSize > 100) PageSize = 100; // Maximum limit
    }
}
