namespace Application.Contracts.DTOs;

/// <summary>
/// B2C ürün filtreleme parametreleri için DTO
/// </summary>
public class ProductFilterDto
{
    public string? PageType { get; set; } // "all", "category", "new-season", "discount"
    public string? CategorySlug { get; set; }
    public List<int>? CategoryIds { get; set; }
    public List<int>? BrandIds { get; set; }
    public List<string>? Sizes { get; set; }
    public Dictionary<string, List<string>>? VariantAttributes { get; set; } // Key: AttributeName, Value: List of selected values
    public decimal? PriceMin { get; set; }
    public decimal? PriceMax { get; set; }
    public bool? InStock { get; set; }
    public string? SearchTerm { get; set; }
    public string? SortBy { get; set; } = "title"; // "title", "price", "created"
    public string? SortDirection { get; set; } = "asc"; // "asc", "desc"
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 12;
}

/// <summary>
/// B2C ürün arama ve filtreleme request parametreleri için DTO
/// </summary>
public class ProductSearchRequestDto
{
    public string? Query { get; set; }
    public string? PageType { get; set; }
    public string? CategorySlug { get; set; }
    public List<int>? Categories { get; set; }
    public List<int>? Brands { get; set; }
    public List<string>? Sizes { get; set; }
    public Dictionary<string, List<string>>? VariantAttributes { get; set; }
    public decimal? PriceMin { get; set; }
    public decimal? PriceMax { get; set; }
    public bool? InStock { get; set; }
    public string? Sort { get; set; } // "price:asc", "price:desc", "title:asc", "title:desc"
    public int Page { get; set; } = 1;
    public int Limit { get; set; } = 12;
}
