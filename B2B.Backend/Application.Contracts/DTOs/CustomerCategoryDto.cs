namespace Application.Contracts.DTOs;

/// <summary>
/// B2C müşteri kategori için DTO - Frontend Category modeline uygun
/// </summary>
public class CustomerCategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Slug { get; set; } = null!;
    public string? Image { get; set; }
    public string? Description { get; set; }
    public CustomerCategoryMetadataDto Metadata { get; set; } = new();
}

/// <summary>
/// B2C müşteri kategori metadata bilgileri için DTO
/// </summary>
public class CustomerCategoryMetadataDto
{
    public string Title { get; set; } = null!;
    public string Description { get; set; } = null!;
    public string? Keywords { get; set; }
    public string? CanonicalUrl { get; set; }
}
