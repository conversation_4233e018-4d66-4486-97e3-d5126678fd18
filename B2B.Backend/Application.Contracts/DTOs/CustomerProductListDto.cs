namespace Application.Contracts.DTOs;

/// <summary>
/// B2C müşteri ürün listesi için DTO - Frontend ProductList modeline uygun
/// </summary>
public class CustomerProductListDto
{
    public CustomerFiltersDto Filters { get; set; } = new();
    public List<CustomerListProductDto> Products { get; set; } = [];
    public CustomerPaginationDto Pagination { get; set; } = new();
    public PageMetadataDto? Metadata { get; set; }
}

/// <summary>
/// B2C müşteri liste ürünü için DTO
/// </summary>
public class CustomerListProductDto
{
    public int Id { get; set; }
    public string Title { get; set; } = null!;
    public string Slug { get; set; } = null!;
    public string? Brand { get; set; }
    public decimal Price { get; set; }
    public decimal? OldPrice { get; set; }
    public string? ImgSrc { get; set; }
    public string? ImgHover { get; set; }
    public string? DiscountType { get; set; }
    public decimal? DiscountPercentage { get; set; }
    public int? CountdownTimer { get; set; }
    public bool InStock { get; set; }
    public string? Description { get; set; }
    public string? Category { get; set; }
    public List<CustomerSizeFilterDto> Sizes { get; set; } = [];
}

/// <summary>
/// B2C müşteri filtreleri için DTO
/// </summary>
public class CustomerFiltersDto
{
    public List<CustomerCategoryFilterDto> Categories { get; set; } = [];
    public CustomerAvailabilityFilterDto Availability { get; set; } = new();
    public CustomerPriceFilterDto Price { get; set; } = new();
    public List<CustomerBrandFilterDto> Brands { get; set; } = [];
    public List<CustomerSizeFilterDto> Sizes { get; set; } = [];
    public List<CustomerVariantAttributeFilterDto> VariantAttributes { get; set; } = [];
    public CustomerSortFilterDto Sort { get; set; } = new();
}

/// <summary>
/// B2C müşteri kategori filtresi için DTO
/// </summary>
public class CustomerCategoryFilterDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Slug { get; set; } = null!;
    public int Count { get; set; }
}

/// <summary>
/// B2C müşteri stok durumu filtresi için DTO
/// </summary>
public class CustomerAvailabilityFilterDto
{
    public bool InStock { get; set; }
    public int InStockCount { get; set; }
    public bool OutOfStock { get; set; }
    public int OutOfStockCount { get; set; }
}

/// <summary>
/// B2C müşteri fiyat aralığı filtresi için DTO
/// </summary>
public class CustomerPriceFilterDto
{
    public decimal MinPrice { get; set; }
    public decimal MaxPrice { get; set; }
}

/// <summary>
/// B2C müşteri marka filtresi için DTO
/// </summary>
public class CustomerBrandFilterDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public int Count { get; set; }
}

/// <summary>
/// B2C müşteri boyut filtresi için DTO
/// </summary>
public class CustomerSizeFilterDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public int Count { get; set; }
}

/// <summary>
/// B2C müşteri sıralama filtresi için DTO
/// </summary>
public class CustomerSortFilterDto
{
    public string Field { get; set; } = "title";
    public string Direction { get; set; } = "asc";
}

/// <summary>
/// B2C müşteri sayfalama bilgisi için DTO
/// </summary>
public class CustomerPaginationDto
{
    public int Page { get; set; } = 1;
    public int ItemCount { get; set; }
    public int ItemsPerPage { get; set; } = 12;
    public int TotalPages { get; set; }
}

/// <summary>
/// B2C müşteri varyant nitelik filtresi için DTO
/// </summary>
public class CustomerVariantAttributeFilterDto
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string ShortName { get; set; } = null!;
    public List<CustomerVariantAttributeValueFilterDto> Values { get; set; } = [];
}

/// <summary>
/// B2C müşteri varyant nitelik değer filtresi için DTO
/// </summary>
public class CustomerVariantAttributeValueFilterDto
{
    public int Id { get; set; }
    public string Value { get; set; } = null!;
    public int Count { get; set; }
}
