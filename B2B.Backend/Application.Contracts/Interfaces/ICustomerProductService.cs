using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

/// <summary>
/// B2C müşteri ürün servisi için interface - Server-side rendering ve SEO odaklı
/// </summary>
public interface ICustomerProductService
{
    // Unified Product List API - Tüm sayfa türleri için (/urunler, /kategoriler, /yeni-sezon, /indirim)
    Task<ProductPageDataDto> GetProductPageDataAsync(ProductFilterDto filter);
    Task<CustomerProductListDto> GetProductListAsync(ProductFilterDto filter);
    
    // Product Detail API - SEO metadata ile
    Task<CustomerProductDto?> GetProductBySlugAsync(string slug);
    Task<ProductDetailPageDataDto?> GetProductDetailPageDataAsync(string slug);
    
    // Product Reviews API
    Task<List<CustomerProductReviewDto>> GetProductReviewsAsync(Guid productId, int page = 1, int pageSize = 10);
    Task<int> GetProductReviewCountAsync(Guid productId);
    Task<decimal> GetProductAverageRatingAsync(Guid productId);
    
    // Featured and Recommended Products API - Server-side rendering için
    Task<List<CustomerListProductDto>> GetFeaturedProductsAsync(int count = 12);
    Task<List<CustomerListProductDto>> GetRecommendedProductsAsync(int count = 12);
    Task<List<CustomerListProductDto>> GetNewSeasonProductsAsync(int count = 12);
    Task<List<CustomerListProductDto>> GetDiscountedProductsAsync(int count = 12);
    Task<List<CustomerListProductDto>> GetRelatedProductsAsync(Guid productId, int count = 8);
    
    // Product Search API - Server-side rendering için
    Task<ProductPageDataDto> SearchProductsAsync(ProductSearchRequestDto request);
    Task<List<CustomerListProductDto>> SearchProductsSimpleAsync(string query, int count = 12);
    
    // Filter Options API - Server-side rendering için
    Task<ProductFilterOptionsDto> GetFilterOptionsAsync(string? pageType = null, string? categorySlug = null);
    
    // Page Metadata API - SEO için
    Task<PageMetadataDto> GetPageMetadataAsync(PageMetadataRequestDto request);
    Task<ProductPageMetadataDto> GetProductMetadataAsync(string slug);
    Task<CategoryPageMetadataDto> GetCategoryMetadataAsync(string categorySlug);
    
    // Category Products API
    Task<CategoryPageDataDto> GetCategoryPageDataAsync(string categorySlug, ProductFilterDto filter);
    Task<List<CustomerListProductDto>> GetProductsByCategoryAsync(string categorySlug, ProductFilterDto filter);
    
    // Home Page Data API
    Task<HomePageDataDto> GetHomePageDataAsync();
    
    // Product Availability and Stock
    Task<bool> IsProductInStockAsync(Guid productId);
    Task<int> GetProductStockQuantityAsync(Guid productId);
    Task<bool> IsProductActiveAsync(Guid productId);
    
    // Product Variants
    Task<List<CustomerVariantOptionDto>> GetProductColorsAsync(Guid productId);
    Task<List<CustomerVariantOptionDto>> GetProductSizesAsync(Guid productId);
    Task<List<CustomerProductImageDto>> GetProductImagesAsync(Guid productId);
    
    // Analytics and Statistics (for admin)
    Task<int> GetTotalProductCountAsync();
    Task<int> GetActiveProductCountAsync();
    Task<int> GetInStockProductCountAsync();
    Task<decimal> GetAverageProductPriceAsync();
}
