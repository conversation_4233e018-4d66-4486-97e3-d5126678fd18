using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

/// <summary>
/// B2C müşteri kategori servisi için interface - Server-side rendering ve SEO odaklı
/// </summary>
public interface ICustomerCategoryService
{
    // Category List API - Server-side rendering için
    Task<List<CustomerCategoryDto>> GetCategoriesAsync();
    Task<List<CustomerCategoryDto>> GetMainCategoriesAsync();
    Task<List<CustomerCategoryDto>> GetSubCategoriesAsync(Guid parentId);
    Task<List<CustomerCategoryDto>> GetCategoryHierarchyAsync();
    
    // Category Detail API - SEO metadata ile
    Task<CustomerCategoryDto?> GetCategoryBySlugAsync(string slug);
    Task<CustomerCategoryDto?> GetCategoryByIdAsync(Guid id);
    
    // Category Products API
    Task<CategoryPageDataDto> GetCategoryPageDataAsync(string categorySlug, ProductFilterDto filter);
    Task<List<CustomerListProductDto>> GetCategoryProductsAsync(string categorySlug, ProductFilterDto filter);
    Task<int> GetCategoryProductCountAsync(string categorySlug);
    
    // Category Navigation API
    Task<List<CustomerCategoryDto>> GetCategoryBreadcrumbAsync(string categorySlug);
    Task<List<CustomerCategoryDto>> GetSiblingCategoriesAsync(string categorySlug);
    Task<List<CustomerCategoryDto>> GetChildCategoriesAsync(string categorySlug);
    
    // Category SEO API
    Task<CategoryPageMetadataDto> GetCategoryMetadataAsync(string categorySlug);
    
    // Category Statistics
    Task<int> GetTotalCategoryCountAsync();
    Task<int> GetActiveCategoryCountAsync();
    Task<List<CustomerCategoryDto>> GetPopularCategoriesAsync(int count = 10);
    Task<List<CustomerCategoryDto>> GetFeaturedCategoriesAsync(int count = 6);
    
    // Category Search
    Task<List<CustomerCategoryDto>> SearchCategoriesAsync(string query, int count = 10);
    
    // Category Validation
    Task<bool> IsCategoryActiveAsync(string categorySlug);
    Task<bool> CategoryExistsAsync(string categorySlug);
}
