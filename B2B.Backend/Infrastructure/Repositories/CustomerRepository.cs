using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Infrastructure.Repositories;

public class CustomerRepository : GenericRepository<Customer>, ICustomerRepository
{
    private readonly B2BDbContext _context;

    public CustomerRepository(B2BDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<Customer?> GetByEmailAsync(string email)
    {
        return await _context.Customers
            .FirstOrDefaultAsync(c => c.Email == email && !c.IsDeleted);
    }

    public async Task<Customer?> FindByEmailHashAsync(string emailHash)
    {
        return await _context.Customers.FirstOrDefaultAsync(c => c.EmailHash == emailHash && !c.IsDeleted);
    }
    public async Task<Customer?> GetByPhoneAsync(string phoneNumber)
    {
        return await _context.Customers
            .FirstOrDefaultAsync(c => c.PhoneNumber == phoneNumber && !c.IsDeleted);
    }

    public async Task<Customer?> GetByTaxOrIdentityNumberAsync(string taxOrIdentityNumber)
    {
        return await _context.Customers
            .FirstOrDefaultAsync(c => c.TaxOrIdentityNumber == taxOrIdentityNumber && !c.IsDeleted);
    }

    public async Task<Customer?> GetWithAddressesAsync(Guid id)
    {
        return await _context.Customers
            .Include(c => c.Addresses.Where(a => !a.IsDeleted))
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<Customer?> GetWithOrdersAsync(Guid id)
    {
        return await _context.Customers
            .Include(c => c.Orders.Where(o => !o.IsDeleted))
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<Customer?> GetWithPointsAsync(Guid id)
    {
        return await _context.Customers
            .Include(c => c.Points.Where(p => !p.IsDeleted))
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<Customer?> GetWithCouponsAsync(Guid id)
    {
        return await _context.Customers
            .Include(c => c.Coupons.Where(cp => !cp.IsDeleted))
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<Customer?> GetWithAllRelatedDataAsync(Guid id)
    {
        return await _context.Customers
            .Include(c => c.Addresses.Where(a => !a.IsDeleted))
            .Include(c => c.Orders.Where(o => !o.IsDeleted))
                .ThenInclude(o => o.OrderRows)
            .Include(c => c.Points.Where(p => !p.IsDeleted))
            .Include(c => c.Coupons.Where(cp => !cp.IsDeleted))
            .Include(c => c.Reviews.Where(r => !r.IsDeleted))
            .Include(c => c.Favourites.Where(f => !f.IsDeleted))
            .Include(c => c.Cart)
                .ThenInclude(cart => cart.Items.Where(ci => !ci.IsDeleted))
            .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
    }

    public async Task<IEnumerable<Customer>> SearchAsync(string searchTerm)
    {
        return await _context.Customers
            .Where(c => !c.IsDeleted &&
                       (c.NameSurname.Contains(searchTerm) ||
                        (c.Email != null && c.Email.Contains(searchTerm)) ||
                        (c.PhoneNumber != null && c.PhoneNumber.Contains(searchTerm))))
            .OrderBy(c => c.NameSurname)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
    {
        return await _context.Customers
            .Where(c => c.IsActive && !c.IsDeleted)
            .OrderBy(c => c.NameSurname)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetInactiveCustomersAsync()
    {
        return await _context.Customers
            .Where(c => !c.IsActive && !c.IsDeleted)
            .OrderBy(c => c.NameSurname)
            .ToListAsync();
    }

    public async Task<IEnumerable<Customer>> GetCustomersByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.Customers
            .Where(c => !c.IsDeleted && c.CreatedAt >= startDate && c.CreatedAt <= endDate)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();
    }

    public async Task<int> GetTotalCustomersCountAsync()
    {
        return await _context.Customers.CountAsync(c => !c.IsDeleted);
    }

    public async Task<int> GetActiveCustomersCountAsync()
    {
        return await _context.Customers.CountAsync(c => c.IsActive && !c.IsDeleted);
    }

    public async Task<int> GetNewCustomersCountAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.Customers
            .CountAsync(c => !c.IsDeleted && c.CreatedAt >= startDate && c.CreatedAt <= endDate);
    }

    public async Task<decimal> GetCustomerTotalSpentAsync(Guid customerId)
    {
        return await _context.Orders
            .Where(o => o.CustomerId == customerId && !o.IsDeleted)
            .SumAsync(o => o.TotalAmount);
    }

    public async Task<int> GetCustomerOrderCountAsync(Guid customerId)
    {
        return await _context.Orders
            .CountAsync(o => o.CustomerId == customerId && !o.IsDeleted);
    }

    public async Task<bool> IsEmailExistsAsync(string email, Guid? excludeCustomerId = null)
    {
        var query = _context.Customers.Where(c => c.Email == email && !c.IsDeleted);

        if (excludeCustomerId.HasValue)
            query = query.Where(c => c.Id != excludeCustomerId.Value);

        return await query.AnyAsync();
    }
    public async Task<bool> IsEmailHashExistsAsync(string email)
    {
        return await _context.Customers.Where(c => c.EmailHash == email && !c.IsDeleted).AnyAsync();
    }
    public async Task<bool> IsPhoneExistsAsync(string phoneNumber, Guid? excludeCustomerId = null)
    {
        var query = _context.Customers.Where(c => c.PhoneNumber == phoneNumber && !c.IsDeleted);

        if (excludeCustomerId.HasValue)
            query = query.Where(c => c.Id != excludeCustomerId.Value);

        return await query.AnyAsync();
    }
    public async Task<bool> IsTaxOrIdentityNumberExistsAsync(string taxOrIdentityNumber, Guid? excludeCustomerId = null)
    {
        var query = _context.Customers.Where(c => c.TaxOrIdentityNumber == taxOrIdentityNumber && !c.IsDeleted);

        if (excludeCustomerId.HasValue)
            query = query.Where(c => c.Id != excludeCustomerId.Value);

        return await query.AnyAsync();
    }

    public async Task AddToHistoryAsync(Customer customer, ChangeType changeType, Guid employeeId)
    {
        var history = new CustomerHistory
        {
            Id = Guid.CreateVersion7(),
            EntityId = customer.Id,
            ChangeType = changeType,
            ChangeDate = DateTime.UtcNow,
            EmployeeId = employeeId,
            CreatedAt = customer.CreatedAt,
            UpdatedAt = customer.UpdatedAt,
            IsDeleted = customer.IsDeleted,
            IsActive = customer.IsActive,

            // Copy entity properties (encrypted fields remain encrypted in history)
            NameSurname = customer.NameSurname,
            Password = customer.Password,
            PhoneNumber = customer.PhoneNumber,
            Email = customer.Email,
            TaxOrIdentityNumber = customer.TaxOrIdentityNumber,
            TaxOffice = customer.TaxOffice,
            CartId = customer.CartId
        };

        await _context.CustomerHistory.AddAsync(history);
    }
}
