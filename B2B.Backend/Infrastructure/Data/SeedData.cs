using Core.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Infrastructure.Data;

public static class SeedData
{
    public static async Task SeedRolesAndPermissions(RoleManager<B2BRole> roleManager)
    {
        // Yeni roller
        var roles = new[] { "Developer", "Admin", "Satış Yönetimi", "<PERSON><PERSON><PERSON>", "Ürün Yönetimi", "Mail Yönetimi" };

        // Genişletilmiş kaynaklar ve eylemler
        var resources = new[] {
            "product", "product_categories", "product_brands", "product_attributes", "product_reviews",
            "order", "customer", "role", "user", "payment", "shipping", "mail", "settings", "dashboard", "erp", "coupon"
        };
        var actions = new[] { "read", "create", "update", "delete" };

        // Developer rolü - Tüm yetkilere sahip
        var developerRole = await EnsureRoleExists(roleManager, "Developer");
        var developerPermissions = new Dictionary<string, Dictionary<string, bool>>
        {
            ["all"] = actions.ToDictionary(a => a, _ => true)
        };
        developerRole.SetRoleClaims(developerPermissions);
        await roleManager.UpdateAsync(developerRole);

        // Admin rolü - Tüm yetkilere sahip (Developer hariç)
        var adminRole = await EnsureRoleExists(roleManager, "Admin");
        var adminPermissions = new Dictionary<string, Dictionary<string, bool>>();
        foreach (var resource in resources)
        {
            adminPermissions[resource] = actions.ToDictionary(a => a, _ => true);
        }
        adminRole.SetRoleClaims(adminPermissions);
        await roleManager.UpdateAsync(adminRole);

        // Satış Yönetimi rolü
        var salesRole = await EnsureRoleExists(roleManager, "Satış Yönetimi");
        var salesPermissions = new Dictionary<string, Dictionary<string, bool>>();
        var salesResources = new[] { "order", "customer", "product", "mail", "dashboard" };
        foreach (var resource in salesResources)
        {
            salesPermissions[resource] = new Dictionary<string, bool>
            {
                ["read"] = true,
                ["create"] = resource == "order" || resource == "customer",
                ["update"] = resource == "order" || resource == "customer",
                ["delete"] = false
            };
        }
        salesRole.SetRoleClaims(salesPermissions);
        await roleManager.UpdateAsync(salesRole);

        // Analiz rolü
        var analysisRole = await EnsureRoleExists(roleManager, "Analiz");
        var analysisPermissions = new Dictionary<string, Dictionary<string, bool>>();
        var analysisResources = new[] { "dashboard", "order", "customer", "product" };
        foreach (var resource in analysisResources)
        {
            analysisPermissions[resource] = new Dictionary<string, bool>
            {
                ["read"] = true,
                ["create"] = false,
                ["update"] = false,
                ["delete"] = false
            };
        }
        analysisRole.SetRoleClaims(analysisPermissions);
        await roleManager.UpdateAsync(analysisRole);

        // Ürün Yönetimi rolü
        var productRole = await EnsureRoleExists(roleManager, "Ürün Yönetimi");
        var productPermissions = new Dictionary<string, Dictionary<string, bool>>();
        var productResources = new[] { "product", "product_categories", "product_brands", "product_attributes", "product_reviews" };
        foreach (var resource in productResources)
        {
            productPermissions[resource] = new Dictionary<string, bool>
            {
                ["read"] = true,
                ["create"] = true,
                ["update"] = true,
                ["delete"] = resource != "product_reviews" // Yorumları silemez
            };
        }
        productRole.SetRoleClaims(productPermissions);
        await roleManager.UpdateAsync(productRole);

        // Mail Yönetimi rolü
        var mailRole = await EnsureRoleExists(roleManager, "Mail Yönetimi");
        var mailPermissions = new Dictionary<string, Dictionary<string, bool>>();
        var mailResources = new[] { "mail", "dashboard" };
        foreach (var resource in mailResources)
        {
            mailPermissions[resource] = new Dictionary<string, bool>
            {
                ["read"] = true,
                ["create"] = resource == "mail",
                ["update"] = resource == "mail",
                ["delete"] = resource == "mail"
            };
        }
        mailRole.SetRoleClaims(mailPermissions);
        await roleManager.UpdateAsync(mailRole);
    }

    private static async Task<B2BRole> EnsureRoleExists(RoleManager<B2BRole> roleManager, string roleName)
    {
        var role = await roleManager.FindByNameAsync(roleName);
        if (role == null)
        {
            role = new B2BRole { Name = roleName, NormalizedName = roleName.ToUpper() };
            await roleManager.CreateAsync(role);
        }
        return role;
    }

    public static async Task SeedAllData(B2BDbContext context, UserManager<B2BUser> userManager, RoleManager<B2BRole> roleManager)
    {
        // Önce permission resources oluşturulur
        await SeedPermissionResources(context);

        // Sonra roller ve izinler oluşturulur
        await SeedRolesAndPermissions(roleManager);

        // Ürün kategorileri oluşturulur
        //await EntitySeeders.SeedProductCategories(context);

        // Ürün nitelikleri ve değerleri oluşturulur
        //await EntitySeeders.SeedProductAttributes(context);

        // Bayiler oluşturulur
        //await EntitySeeders.SeedDealers(context);

        // Ürünler oluşturulur
        //await EntitySeeders.SeedProducts(context);

        // Kullanıcılar oluşturulur
        await EntitySeeders.SeedUsers(context, userManager);

        // Müşteriler oluşturulur
        // await EntitySeeders.SeedCustomers(context);

        // Siparişler oluşturulur
        // await EntitySeeders.SeedOrders(context);
    }

    public static async Task SeedPermissionResources(B2BDbContext context)
    {
        var resources = new[]
        {
            new PermissionResource { Key = "product", Label = "Ürünler", Description = "Ürün yönetimi", SortOrder = 1 },
            new PermissionResource { Key = "product_categories", Label = "Ürün Kategorileri", Description = "Ürün kategori yönetimi", SortOrder = 2 },
            new PermissionResource { Key = "product_brands", Label = "Ürün Markaları", Description = "Ürün marka yönetimi", SortOrder = 3 },
            new PermissionResource { Key = "product_attributes", Label = "Ürün Nitelikleri", Description = "Ürün nitelik yönetimi", SortOrder = 4 },
            new PermissionResource { Key = "product_reviews", Label = "Ürün Yorumları", Description = "Ürün yorum yönetimi", SortOrder = 5 },
            new PermissionResource { Key = "customer", Label = "B2C Müşteriler", Description = "B2C müşteri yönetimi", SortOrder = 6 },
            new PermissionResource { Key = "address", Label = "Adres Yönetimi", Description = "Müşteri ve bayi adres yönetimi", SortOrder = 7 },
            new PermissionResource { Key = "cart", Label = "Sepet Yönetimi", Description = "Müşteri sepet yönetimi ve analitikleri", SortOrder = 8 },
            new PermissionResource { Key = "user_point", Label = "Puan Yönetimi", Description = "Müşteri puan sistemi yönetimi", SortOrder = 9 },
            new PermissionResource { Key = "coupon", Label = "Kupon Yönetimi", Description = "Müşteri kupon sistemi yönetimi", SortOrder = 10 },
            new PermissionResource { Key = "order", Label = "Siparişler", Description = "Sipariş yönetimi", SortOrder = 10 },
            new PermissionResource { Key = "customer", Label = "Müşteriler", Description = "Müşteri yönetimi", SortOrder = 11 },
            new PermissionResource { Key = "user", Label = "Kullanıcılar", Description = "Kullanıcı yönetimi", SortOrder = 20 },
            new PermissionResource { Key = "role", Label = "Roller", Description = "Rol ve yetki yönetimi", SortOrder = 21 },
            new PermissionResource { Key = "payment", Label = "Ödemeler", Description = "Ödeme yönetimi", SortOrder = 30 },
            new PermissionResource { Key = "shipping", Label = "Kargolar", Description = "Kargo yönetimi", SortOrder = 31 },
            new PermissionResource { Key = "mail", Label = "Mail Şablonları", Description = "Mail şablonu yönetimi", SortOrder = 32 },
            new PermissionResource { Key = "settings", Label = "Ayarlar", Description = "Sistem ayarları", SortOrder = 40 },
            new PermissionResource { Key = "dashboard", Label = "Dashboard", Description = "Dashboard ve raporlar", SortOrder = 41 },
            new PermissionResource { Key = "erp", Label = "ERP Entegrasyonu", Description = "ERP entegrasyon yönetimi", SortOrder = 50 }
        };

        foreach (var resource in resources)
        {
            var existing = await context.PermissionResources
                .FirstOrDefaultAsync(pr => pr.Key == resource.Key);

            if (existing == null)
            {
                context.PermissionResources.Add(resource);
            }
        }

        await context.SaveChangesAsync();
    }
}
