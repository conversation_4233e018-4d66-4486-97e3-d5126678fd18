using Application.Contracts.Interfaces;
using Application.Contracts.DTOs;
using Core.Entities;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

public class ProductAttributeService : IProductAttributeService
{
    private readonly IGenericRepository<ProductAttribute> _repository;
    private readonly IGenericRepository<ProductCategory> _categoryRepository;

    public ProductAttributeService(
        IGenericRepository<ProductAttribute> repository,
        IGenericRepository<ProductCategory> categoryRepository)
    {
        _repository = repository;
        _categoryRepository = categoryRepository;
    }
    public async Task<List<ProductAttributeDto>> GetListAsync(int? page, int? pageSize, Guid? categoryId = null, bool? isVariantAttribute = null)
    {
        try
        {
            // Query'yi repository'den al ve filtreleri uygula
            var query = _repository.Query();

            // Kategori filtresi
            if (categoryId.HasValue)
            {
                query = query.Where(a =>
                    a.Categories.Any(c => c.Id == categoryId.Value) || !a.Categories.Any());
            }

            // Varyant nitelik filtresi
            if (isVariantAttribute.HasValue)
            {
                query = query.Where(a => a.IsVariantAttribute == isVariantAttribute.Value);
            }

            // Sayfalama uygula
            if (page.HasValue && pageSize.HasValue)
            {
                query = query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
            }

            var attributes = await query.Include(a => a.Values).Include(a => a.Categories).ToListAsync();

            return attributes.Select(attribute => new ProductAttributeDto
            {
                Id = attribute.Id,
                Name = attribute.Name,
                ShortName = attribute.ShortName,
                IsVariantAttribute = attribute.IsVariantAttribute,
                IsListAttribute = attribute.IsListAttribute,
                Values = attribute.Values.Select(v => new ProductAttributeValueDto
                {
                    Id = v.Id,
                    AttributeId = v.AttributeId,
                    Value = v.Value
                }).ToList(),
                Categories = attribute.Categories.Select(c => new ProductCategoryDto
                {
                    Id = c.Id,
                    Name = c.Name
                }).ToList()
            }).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine("ProductAttributeError"+ ex.Message);
            return [];
        }
    }

    public async Task<ProductAttributeDto?> GetByIdAsync(Guid id)
    {
        var attribute = await _repository.Query()
            .Include(a => a.Values)
            .Include(a => a.Categories)
            .FirstOrDefaultAsync(a => a.Id == id);
        if (attribute == null)
        {
            return null;
        }

        var attributeDto = new ProductAttributeDto
        {
            Id = attribute.Id,
            Name = attribute.Name,
            ShortName = attribute.ShortName,
            IsVariantAttribute = attribute.IsVariantAttribute,
            IsListAttribute = attribute.IsListAttribute,
            Values = attribute.Values.Select(v => new ProductAttributeValueDto
            {
                Id = v.Id,
                Value = v.Value
            }).ToList(),
            Categories = attribute.Categories.Select(c => new ProductCategoryDto
            {
                Id = c.Id,
                Name = c.Name
            }).ToList()
        };

        return attributeDto;
    }

    public async Task<List<ProductAttributeDto>> GetByCategoryIdAsync(Guid categoryId)
    {
        try
        {
            // Category'ye göre TÜM attribute'ları getir (IsVariantAttribute=true VEYA IsListAttribute=true)
            var query = _repository.Query()
                .Where(a => a.Categories.Any(c => c.Id == categoryId) || !a.Categories.Any())
                .Where(a => a.IsVariantAttribute || a.IsListAttribute)
                .Include(a => a.Values)
                .Include(a => a.Categories);

            var attributes = await query.ToListAsync();

            return attributes.Select(attribute => new ProductAttributeDto
            {
                Id = attribute.Id,
                Name = attribute.Name,
                ShortName = attribute.ShortName,
                IsVariantAttribute = attribute.IsVariantAttribute,
                IsListAttribute = attribute.IsListAttribute,
                Values = attribute.Values.Select(v => new ProductAttributeValueDto
                {
                    Id = v.Id,
                    AttributeId = v.AttributeId,
                    Value = v.Value
                }).ToList(),
                Categories = attribute.Categories.Select(c => new ProductCategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Slug = c.Slug
                }).ToList()
            }).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine("GetByCategoryIdAsync Error: " + ex.Message);
            return [];
        }
    }

    public async Task CreateAsync(ProductAttributeCreateDto dto)
    {
        var productAttribute = new ProductAttribute
        {
            Name = dto.Name,
            ShortName = dto.ShortName,
            IsVariantAttribute = dto.IsVariantAttribute,
            IsListAttribute = dto.IsListAttribute,
            Categories = new List<ProductCategory>()
        };

        // Category'leri veritabanından çek ve ekle
        if (dto.CategoryIds.Count > 0)
        {
            var categories = await _categoryRepository.Query()
                .Where(c => dto.CategoryIds.Contains(c.Id))
                .ToListAsync();
            foreach (var category in categories)
            {
                productAttribute.Categories.Add(category);
            }
        }

        foreach (var value in dto.Values)
        {
            productAttribute.Values.Add(new ProductAttributeValue
            {
                Id = Guid.NewGuid(),
                Value = value.Value
            });
        }

        await _repository.AddAsync(productAttribute);
        await _repository.SaveChangesAsync();
    }

    public async Task UpdateAsync(ProductAttributeUpdateDto dto)
    {
        var productAttribute = await _repository.Query()
            .Include(p => p.Categories)
            .FirstOrDefaultAsync(p=>p.Id == dto.Id);
        if (productAttribute == null)
        {
            throw new ArgumentException("Product attribute not found");
        }

        productAttribute.Name = dto.Name;
        productAttribute.ShortName = dto.ShortName;
        productAttribute.IsVariantAttribute = dto.IsVariantAttribute;
        productAttribute.IsListAttribute = dto.IsListAttribute;
        productAttribute.Categories.Clear();
        productAttribute.Values.Clear();

        // Category'leri veritabanından çek ve ekle
        if (dto.CategoryIds.Count > 0)
        {
            var categories = await _categoryRepository.Query()
                .Where(c => dto.CategoryIds.Contains(c.Id))
                .ToListAsync();
            foreach (var category in categories)
            {
                productAttribute.Categories.Add(category);
            }
        }

        foreach (var value in productAttribute.Values)
        {
            productAttribute.Values.Add(new ProductAttributeValue
            {
                Id = Guid.NewGuid(),
                Value = value.Value
            });
        }
        _repository.Update(productAttribute);
        await _repository.SaveChangesAsync();
    }

    public async Task DeleteAsync(Guid id)
    {
        var productAttribute = await _repository.GetByIdAsync(id);
        if (productAttribute == null)
        {
            throw new ArgumentException("Product attribute not found");
        }

        _repository.Delete(productAttribute);
        await _repository.SaveChangesAsync();
    }
}
