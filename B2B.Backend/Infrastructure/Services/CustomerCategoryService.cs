using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services;

/// <summary>
/// B2C müşteri kategori servisi implementasyonu - Server-side rendering ve SEO odaklı
/// </summary>
public class CustomerCategoryService : ICustomerCategoryService
{
    private readonly IGenericRepository<ProductCategory> _categoryRepository;
    private readonly IGenericRepository<Product> _productRepository;
    private readonly ICustomerProductService _customerProductService;

    public CustomerCategoryService(
        IGenericRepository<ProductCategory> categoryRepository,
        IGenericRepository<Product> productRepository,
        ICustomerProductService customerProductService)
    {
        _categoryRepository = categoryRepository;
        _productRepository = productRepository;
        _customerProductService = customerProductService;
    }

    public async Task<List<CustomerCategoryDto>> GetCategoriesAsync()
    {
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted)
            .OrderBy(c => c.Name)
            .ToListAsync();

        return categories.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<List<CustomerCategoryDto>> GetMainCategoriesAsync()
    {
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.ParentId == null)
            .OrderBy(c => c.Name)
            .ToListAsync();

        return categories.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<List<CustomerCategoryDto>> GetSubCategoriesAsync(Guid parentId)
    {
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.ParentId == parentId)
            .OrderBy(c => c.Name)
            .ToListAsync();

        return categories.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<List<CustomerCategoryDto>> GetCategoryHierarchyAsync()
    {
        var allCategories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted)
            .OrderBy(c => c.Name)
            .ToListAsync();

        // Build hierarchy - return only main categories for now
        // In a full implementation, you would build a tree structure
        return allCategories
            .Where(c => c.ParentId == null)
            .Select(MapToCustomerCategoryDto)
            .ToList();
    }

    public async Task<CustomerCategoryDto?> GetCategoryBySlugAsync(string slug)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == slug && c.IsActive && !c.IsDeleted);

        return category != null ? MapToCustomerCategoryDto(category) : null;
    }

    public async Task<CustomerCategoryDto?> GetCategoryByIdAsync(Guid id)
    {
        var category = await _categoryRepository.GetByIdAsync(id);
        
        if (category == null || !category.IsActive || category.IsDeleted)
            return null;

        return MapToCustomerCategoryDto(category);
    }

    public async Task<CategoryPageDataDto> GetCategoryPageDataAsync(string categorySlug, ProductFilterDto filter)
    {
        return await _customerProductService.GetCategoryPageDataAsync(categorySlug, filter);
    }

    public async Task<List<CustomerListProductDto>> GetCategoryProductsAsync(string categorySlug, ProductFilterDto filter)
    {
        return await _customerProductService.GetProductsByCategoryAsync(categorySlug, filter);
    }

    public async Task<int> GetCategoryProductCountAsync(string categorySlug)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted);

        if (category == null) return 0;

        return await _productRepository.CountAsync(p => p.CategoryId == category.Id && p.IsActive && !p.IsDeleted);
    }

    public async Task<List<CustomerCategoryDto>> GetCategoryBreadcrumbAsync(string categorySlug)
    {
        var category = await _categoryRepository.Query()
            .Include(c => c.Parent)
            .FirstOrDefaultAsync(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted);

        if (category == null) return [];

        var breadcrumb = new List<CustomerCategoryDto>();
        var current = category;

        // Build breadcrumb from current category up to root
        while (current != null)
        {
            breadcrumb.Insert(0, MapToCustomerCategoryDto(current));
            current = current.Parent;
        }

        return breadcrumb;
    }

    public async Task<List<CustomerCategoryDto>> GetSiblingCategoriesAsync(string categorySlug)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted);

        if (category == null) return [];

        var siblings = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.ParentId == category.ParentId && c.Id != category.Id)
            .OrderBy(c => c.Name)
            .ToListAsync();

        return siblings.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<List<CustomerCategoryDto>> GetChildCategoriesAsync(string categorySlug)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == categorySlug && c.IsActive && !c.IsDeleted);

        if (category == null) return [];

        var children = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.ParentId == category.Id)
            .OrderBy(c => c.Name)
            .ToListAsync();

        return children.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<CategoryPageMetadataDto> GetCategoryMetadataAsync(string categorySlug)
    {
        return await _customerProductService.GetCategoryMetadataAsync(categorySlug);
    }

    public async Task<int> GetTotalCategoryCountAsync()
    {
        return await _categoryRepository.CountAsync(c => !c.IsDeleted);
    }

    public async Task<int> GetActiveCategoryCountAsync()
    {
        return await _categoryRepository.CountAsync(c => c.IsActive && !c.IsDeleted);
    }

    public async Task<List<CustomerCategoryDto>> GetPopularCategoriesAsync(int count = 10)
    {
        // Get categories with most products
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted)
            .Select(c => new { Category = c, ProductCount = c.Products.Count(p => p.IsActive && !p.IsDeleted) })
            .OrderByDescending(x => x.ProductCount)
            .Take(count)
            .ToListAsync();

        return categories.Select(x => MapToCustomerCategoryDto(x.Category)).ToList();
    }

    public async Task<List<CustomerCategoryDto>> GetFeaturedCategoriesAsync(int count = 6)
    {
        // For now, return main categories. In future, implement featured logic
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.ParentId == null)
            .OrderBy(c => c.Name)
            .Take(count)
            .ToListAsync();

        return categories.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<List<CustomerCategoryDto>> SearchCategoriesAsync(string query, int count = 10)
    {
        var categories = await _categoryRepository.Query()
            .Where(c => c.IsActive && !c.IsDeleted && c.Name.Contains(query))
            .OrderBy(c => c.Name)
            .Take(count)
            .ToListAsync();

        return categories.Select(MapToCustomerCategoryDto).ToList();
    }

    public async Task<bool> IsCategoryActiveAsync(string categorySlug)
    {
        var category = await _categoryRepository.Query()
            .FirstOrDefaultAsync(c => c.Slug == categorySlug);

        return category?.IsActive == true && category?.IsDeleted == false;
    }

    public async Task<bool> CategoryExistsAsync(string categorySlug)
    {
        return await _categoryRepository.AnyAsync(c => c.Slug == categorySlug && !c.IsDeleted);
    }

    // Helper methods
    private CustomerCategoryDto MapToCustomerCategoryDto(ProductCategory category)
    {
        return new CustomerCategoryDto
        {
            Id = (int)category.Id.GetHashCode(), // Convert Guid to int for frontend compatibility
            Name = category.Name,
            Slug = category.Slug,
            Metadata = new CustomerCategoryMetadataDto
            {
                Title = $"{category.Name} - Kategoriler",
                Description = $"{category.Name} kategorisindeki ürünleri keşfedin"
            }
        };
    }
}
