using System.Security.Cryptography;
using System.Text;
using Core.Interfaces;

namespace Infrastructure.Services;

public class EncryptionService : IEncryptionService
{
    // Master key - Bu production'da environment variable'dan alınmalı
    private static readonly string MasterKey = "B2C_CUSTOMER_ENCRYPTION_MASTER_KEY_2025_SECURE_AES256";
    private static readonly int SaltSize = 16; // 128 bit
    private static readonly int KeySize = 32; // 256 bit
    private static readonly int IvSize = 16; // 128 bit
    private static readonly string HmacSecretKey = "B2C_CUSTOMER_HMAC_SECRET_KEY_2025_SECURE_SHA256";

    public string Encrypt(string plainText)
    {
        if (string.IsNullOrEmpty(plainText))
            throw new ArgumentException("Plain text cannot be null or empty", nameof(plainText));

        // Generate random salt
        var salt = GenerateSalt();

        // Derive key from master key and salt
        var key = DeriveKey(MasterKey, salt);

        // Generate random IV
        var iv = GenerateIV();

        using var aes = Aes.Create();
        aes.Key = key;
        aes.IV = iv;
        aes.Mode = CipherMode.CBC;
        aes.Padding = PaddingMode.PKCS7;

        using var encryptor = aes.CreateEncryptor();
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

        // Combine salt + iv + encrypted data
        var result = new byte[SaltSize + IvSize + encryptedBytes.Length];
        Array.Copy(salt, 0, result, 0, SaltSize);
        Array.Copy(iv, 0, result, SaltSize, IvSize);
        Array.Copy(encryptedBytes, 0, result, SaltSize + IvSize, encryptedBytes.Length);

        return Convert.ToBase64String(result);
    }

    public string Decrypt(string encryptedText)
    {
        if (string.IsNullOrEmpty(encryptedText))
            throw new ArgumentException("Encrypted text cannot be null or empty", nameof(encryptedText));

        try
        {
            var encryptedBytes = Convert.FromBase64String(encryptedText);

            if (encryptedBytes.Length < SaltSize + IvSize)
                throw new ArgumentException("Invalid encrypted data format");

            // Extract salt, IV, and encrypted data
            var salt = new byte[SaltSize];
            var iv = new byte[IvSize];
            var encrypted = new byte[encryptedBytes.Length - SaltSize - IvSize];

            Array.Copy(encryptedBytes, 0, salt, 0, SaltSize);
            Array.Copy(encryptedBytes, SaltSize, iv, 0, IvSize);
            Array.Copy(encryptedBytes, SaltSize + IvSize, encrypted, 0, encrypted.Length);

            // Derive key from master key and salt
            var key = DeriveKey(MasterKey, salt);

            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using var decryptor = aes.CreateDecryptor();
            var decryptedBytes = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length);

            return Encoding.UTF8.GetString(decryptedBytes);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to decrypt data", ex);
        }
    }

    public string? EncryptIfNotEmpty(string? plainText)
    {
        return string.IsNullOrEmpty(plainText) ? plainText : Encrypt(plainText);
    }

    public string? DecryptIfNotEmpty(string? encryptedText)
    {
        if (string.IsNullOrEmpty(encryptedText))
            return encryptedText;

        try
        {
            return Decrypt(encryptedText);
        }
        catch (Exception ex)
        {
            // Log the error and return the original text (assuming it's not encrypted)
            Console.WriteLine($"Decryption failed for text: {encryptedText}. Error: {ex.Message}");
            return encryptedText; // Return as-is if decryption fails
        }
    }
    public string GenerateLookupHash(string? plainText)
    {
        if (string.IsNullOrEmpty(plainText))
            return string.Empty;

        // E-posta gibi verilerde büyük/küçük harf tutarlılığı için normalleştirme
        var normalizedText = plainText.ToLowerInvariant().Trim();
        var keyBytes = Encoding.UTF8.GetBytes(HmacSecretKey);
        var textBytes = Encoding.UTF8.GetBytes(normalizedText);

        using (var hmac = new HMACSHA256(keyBytes))
        {
            var hashBytes = hmac.ComputeHash(textBytes);
            return Convert.ToBase64String(hashBytes);
        }
    }
    private static byte[] GenerateSalt()
    {
        var salt = new byte[SaltSize];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(salt);
        return salt;
    }

    private static byte[] GenerateIV()
    {
        var iv = new byte[IvSize];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(iv);
        return iv;
    }

    private static byte[] DeriveKey(string masterKey, byte[] salt)
    {
        using var pbkdf2 = new Rfc2898DeriveBytes(
            Encoding.UTF8.GetBytes(masterKey),
            salt,
            100000, // 100k iterations
            HashAlgorithmName.SHA256
        );
        return pbkdf2.GetBytes(KeySize);
    }
}
