namespace Core.Interfaces;

public interface IEncryptionService
{
    /// <summary>
    /// Encrypts a string value using AES-256 with master key and salt
    /// </summary>
    /// <param name="plainText">The text to encrypt</param>
    /// <returns>Encrypted text with salt (Base64 encoded)</returns>
    string Encrypt(string plainText);

    /// <summary>
    /// Decrypts a string value using AES-256 with master key and salt
    /// </summary>
    /// <param name="encryptedText">The encrypted text with salt (Base64 encoded)</param>
    /// <returns>Decrypted plain text</returns>
    string Decrypt(string encryptedText);

    /// <summary>
    /// Encrypts a string value if it's not null or empty
    /// </summary>
    /// <param name="plainText">The text to encrypt</param>
    /// <returns>Encrypted text or null/empty if input was null/empty</returns>
    string? EncryptIfNotEmpty(string? plainText);

    /// <summary>
    /// Decrypts a string value if it's not null or empty
    /// </summary>
    /// <param name="encryptedText">The encrypted text to decrypt</param>
    /// <returns>Decrypted text or null/empty if input was null/empty</returns>
    string? DecryptIfNotEmpty(string? encryptedText);

    string GenerateLookupHash(string? plainText);
}
