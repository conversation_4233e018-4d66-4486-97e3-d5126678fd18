using Core.Entities;
using System.Linq.Expressions;

namespace Core.Interfaces;

public interface ICustomerRepository : IGenericRepository<Customer>
{
    // Specific customer queries
    Task<Customer?> GetByEmailAsync(string email);
    Task<Customer?> FindByEmailHashAsync(string emailHash);
    Task<Customer?> GetByPhoneAsync(string phoneNumber);
    Task<Customer?> GetByTaxOrIdentityNumberAsync(string taxOrIdentityNumber);

    // Customer with related data
    Task<Customer?> GetWithAddressesAsync(Guid id);
    Task<Customer?> GetWithOrdersAsync(Guid id);
    Task<Customer?> GetWithPointsAsync(Guid id);
    Task<Customer?> GetWithCouponsAsync(Guid id);
    Task<Customer?> GetWithAllRelatedDataAsync(Guid id);

    // Search and filter
    Task<IEnumerable<Customer>> SearchAsync(string searchTerm);
    Task<IEnumerable<Customer>> GetActiveCustomersAsync();
    Task<IEnumerable<Customer>> GetInactiveCustomersAsync();
    Task<IEnumerable<Customer>> GetCustomersByDateRangeAsync(DateTime startDate, DateTime endDate);

    // Statistics
    Task<int> GetTotalCustomersCountAsync();
    Task<int> GetActiveCustomersCountAsync();
    Task<int> GetNewCustomersCountAsync(DateTime startDate, DateTime endDate);
    Task<decimal> GetCustomerTotalSpentAsync(Guid customerId);
    Task<int> GetCustomerOrderCountAsync(Guid customerId);

    // Existence checks
    Task<bool> IsEmailExistsAsync(string email, Guid? excludeCustomerId = null);
    Task<bool> IsPhoneExistsAsync(string phoneNumber, Guid? excludeCustomerId = null);
    Task<bool> IsEmailHashExistsAsync(string email);
    Task<bool> IsTaxOrIdentityNumberExistsAsync(string taxOrIdentityNumber, Guid? excludeCustomerId = null);

    // History operations
    Task AddToHistoryAsync(Customer customer, Core.Enums.ChangeType changeType, Guid employeeId);
}
