using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Attributes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities
{
    [Table("Customers")]
    public partial class Customer : BaseEntity
    {
        [Encrypted("Customer full name - personal data")]
        [Required]
        [MaxLength(400)]
        public string NameSurname { get; set; } = null!;

        [Encrypted("Customer password - sensitive data")]
        [Required]
        [MaxLength(500)]
        public string Password { get; set; } = null!;

        [Encrypted("Customer phone number - personal data")]
        [MaxLength(100)]
        public string? PhoneNumber { get; set; }

        [Encrypted("Customer email address - personal data")]
        [MaxLength(400)]
        public string? Email { get; set; }

        [Encrypted("Customer tax or identity number - personal data")]
        [MaxLength(150)]
        public string? TaxOrIdentityNumber { get; set; }

        [MaxLength(200)]
        public string? TaxOffice { get; set; }
        [MaxLength(200)]
        public string? EmailHash { get; set; }
        public Guid CartId { get; set; }
        public Cart Cart { get; set; } = new();
        public ICollection<Address> Addresses { get; set; } = [];
        public ICollection<Order> Orders { get; set; } = [];
        public ICollection<ProductReview> Reviews { get; set; } = [];
        public ICollection<Favourite> Favourites { get; set; } = [];
        public ICollection<UserPoint> Points { get; set; } = [];
        public ICollection<DealerUser> DealerUsers { get; set; } = [];
        public ICollection<Coupon> Coupons { get; set; } = [];

        public static void Configure(EntityTypeBuilder<Customer> builder)
        {
            builder.HasIndex(c => c.Email).IsUnique();
            builder.HasIndex(c => c.PhoneNumber);
            builder.HasIndex(c => c.TaxOrIdentityNumber);

            builder.HasOne(c => c.Cart)
                .WithOne(cart => cart.Customer)
                .HasForeignKey<Cart>(cart => cart.CustomerId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }

    [Table("CustomersHistory")]
    public class CustomerHistory : HistoryBaseEntity
    {
        // Entity properties - encrypted fields stored as encrypted in history too
        public string NameSurname { get; set; } = null!;
        public string Password { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? TaxOrIdentityNumber { get; set; }
        public string? TaxOffice { get; set; }
        public Guid CartId { get; set; }
        public string? EmailHash { get; set; }
    }
}
