/**
 * SEO Metadata Management Utilities
 * ProductSEO entegrasyonu ile dinamik metadata yönetimi
 */

// Default metadata values
const DEFAULT_METADATA = {
  siteName: 'Vineta',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com',
  defaultTitle: 'Vineta - Premium Alışveriş Deneyimi',
  defaultDescription: 'En kaliteli ürünleri keşfedin. Geniş ürün yel<PERSON>, hızlı kargo ve güvenli ödeme seçenekleri.',
  defaultKeywords: 'alışveriş, online mağaza, kaliteli ürünler, moda, aksesuar',
  defaultImage: '/images/og-default.jpg',
  twitterHandle: '@vineta',
  locale: 'tr_TR',
  type: 'website'
};

/**
 * Ürün detay sayfası için metadata oluşturur
 * @param {Object} productData - Backend'den gelen ürün verisi
 * @returns {Object} Next.js metadata objesi
 */
export const generateProductMetadata = (productData) => {
  if (!productData?.product) {
    return generateNotFoundMetadata();
  }

  const { product, metadata } = productData;
  
  // ProductSEO'dan gelen metadata'yı kullan, yoksa otomatik oluştur
  const title = metadata?.title || `${product.title} | ${DEFAULT_METADATA.siteName}`;
  const description = metadata?.description || generateProductDescription(product);
  const keywords = metadata?.keywords || generateProductKeywords(product);
  const canonicalUrl = metadata?.canonicalUrl || `${DEFAULT_METADATA.siteUrl}/products/${product.slug}`;
  
  // Open Graph image - ürün görseli varsa kullan
  const ogImage = product.images?.length > 0 
    ? product.images[0].imgSrc 
    : product.imgSrc || DEFAULT_METADATA.defaultImage;

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: 'product',
      url: canonicalUrl,
      siteName: DEFAULT_METADATA.siteName,
      locale: DEFAULT_METADATA.locale,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: product.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: DEFAULT_METADATA.twitterHandle,
      title,
      description,
      images: [ogImage],
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    other: {
      'product:price:amount': product.price,
      'product:price:currency': 'TRY',
      'product:availability': product.inStock ? 'in stock' : 'out of stock',
      'product:brand': product.brand || '',
      'product:category': product.categoryName || '',
    },
  };
};

/**
 * Kategori sayfası için metadata oluşturur
 * @param {Object} categoryData - Backend'den gelen kategori verisi
 * @param {string} slug - Kategori slug'ı
 * @returns {Object} Next.js metadata objesi
 */
export const generateCategoryMetadata = (categoryData, slug) => {
  if (!categoryData) {
    return generateNotFoundMetadata('Kategori Bulunamadı');
  }

  const title = `${categoryData.name} | ${DEFAULT_METADATA.siteName}`;
  const description = `${categoryData.name} kategorisindeki en kaliteli ürünleri keşfedin. Geniş ürün seçenekleri ve uygun fiyatlar.`;
  const canonicalUrl = `${DEFAULT_METADATA.siteUrl}/kategoriler/${slug}`;

  return {
    title,
    description,
    keywords: `${categoryData.name}, kategori, ürünler, ${DEFAULT_METADATA.defaultKeywords}`,
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: DEFAULT_METADATA.siteName,
      locale: DEFAULT_METADATA.locale,
    },
    twitter: {
      card: 'summary',
      site: DEFAULT_METADATA.twitterHandle,
      title,
      description,
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
};

/**
 * Sayfa türüne göre metadata oluşturur
 * @param {string} pageType - Sayfa türü (products, new-season, discounted)
 * @param {string} path - Sayfa yolu
 * @param {Object} customData - Özel metadata verisi
 * @returns {Object} Next.js metadata objesi
 */
export const generatePageMetadata = (pageType, path, customData = {}) => {
  const pageConfig = {
    products: {
      title: 'Tüm Ürünler',
      description: 'En kaliteli ürünleri keşfedin. Geniş ürün yelpazesi ile ihtiyacınız olan her şeyi bulun.',
      keywords: 'ürünler, alışveriş, kaliteli ürünler, online mağaza',
    },
    'new-season': {
      title: 'Yeni Sezon Ürünleri',
      description: 'Yeni sezonun en trend ürünlerini keşfedin. Moda ve stilin öncüsü koleksiyonlar.',
      keywords: 'yeni sezon, trend ürünler, moda, koleksiyon, yeni çıkan ürünler',
    },
    discounted: {
      title: 'İndirimli Ürünler',
      description: 'En iyi fiyatlarla kaliteli ürünler. Büyük indirim fırsatlarını kaçırmayın!',
      keywords: 'indirim, kampanya, ucuz ürünler, fırsat ürünleri, sale',
    },
  };

  const config = pageConfig[pageType] || pageConfig.products;
  const title = customData.title || `${config.title} | ${DEFAULT_METADATA.siteName}`;
  const description = customData.description || config.description;
  const canonicalUrl = `${DEFAULT_METADATA.siteUrl}${path}`;

  return {
    title,
    description,
    keywords: customData.keywords || config.keywords,
    openGraph: {
      title,
      description,
      type: 'website',
      url: canonicalUrl,
      siteName: DEFAULT_METADATA.siteName,
      locale: DEFAULT_METADATA.locale,
    },
    twitter: {
      card: 'summary',
      site: DEFAULT_METADATA.twitterHandle,
      title,
      description,
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
};

/**
 * 404 sayfası için metadata oluşturur
 * @param {string} customTitle - Özel başlık
 * @returns {Object} Next.js metadata objesi
 */
export const generateNotFoundMetadata = (customTitle = 'Sayfa Bulunamadı') => {
  return {
    title: `${customTitle} | ${DEFAULT_METADATA.siteName}`,
    description: 'Aradığınız sayfa bulunamadı. Ana sayfaya dönebilir veya arama yapabilirsiniz.',
    robots: {
      index: false,
      follow: false,
    },
  };
};

/**
 * Ürün için otomatik açıklama oluşturur
 * @param {Object} product - Ürün objesi
 * @returns {string} Ürün açıklaması
 */
const generateProductDescription = (product) => {
  const parts = [product.title];
  
  if (product.brand) {
    parts.push(`${product.brand} markası`);
  }
  
  if (product.categoryName) {
    parts.push(`${product.categoryName} kategorisinde`);
  }
  
  parts.push('en uygun fiyatlarla satın alın');
  
  if (product.inStock) {
    parts.push('Hızlı kargo ve güvenli ödeme seçenekleri');
  }
  
  return parts.join('. ') + '.';
};

/**
 * Ürün için otomatik anahtar kelimeler oluşturur
 * @param {Object} product - Ürün objesi
 * @returns {string} Anahtar kelimeler
 */
const generateProductKeywords = (product) => {
  const keywords = [product.title];
  
  if (product.brand) keywords.push(product.brand);
  if (product.categoryName) keywords.push(product.categoryName);
  if (product.sku) keywords.push(product.sku);
  
  keywords.push(...DEFAULT_METADATA.defaultKeywords.split(', '));
  
  return keywords.join(', ');
};

/**
 * Breadcrumb structured data oluşturur
 * @param {Array} breadcrumbs - Breadcrumb dizisi
 * @returns {Object} JSON-LD structured data
 */
export const generateBreadcrumbStructuredData = (breadcrumbs) => {
  if (!breadcrumbs || breadcrumbs.length === 0) return null;

  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${DEFAULT_METADATA.siteUrl}${item.url}`,
    })),
  };
};

/**
 * Ürün structured data oluşturur
 * @param {Object} product - Ürün objesi
 * @returns {Object} JSON-LD structured data
 */
export const generateProductStructuredData = (product) => {
  if (!product) return null;

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.title,
    description: product.description || generateProductDescription(product),
    sku: product.sku,
    brand: product.brand ? {
      '@type': 'Brand',
      name: product.brand,
    } : undefined,
    category: product.categoryName,
    image: product.images?.map(img => img.imgSrc) || [product.imgSrc],
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'TRY',
      availability: product.inStock 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: DEFAULT_METADATA.siteName,
      },
    },
  };

  // Yorumlar varsa ekle
  if (product.reviews && product.reviews.length > 0) {
    structuredData.aggregateRating = {
      '@type': 'AggregateRating',
      ratingValue: product.reviewRate || 0,
      reviewCount: product.reviewCount || 0,
    };

    structuredData.review = product.reviews.map(review => ({
      '@type': 'Review',
      author: {
        '@type': 'Person',
        name: review.customerName || 'Anonim',
      },
      reviewRating: {
        '@type': 'Rating',
        ratingValue: review.rating,
      },
      reviewBody: review.comment,
      datePublished: review.createdAt,
    }));
  }

  return structuredData;
};
