import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL ?? 'http://localhost:33800/web-api';
const DEFAULT_CACHE_SECONDS = 60;
const urlCache = new Map([['/companyinfo/active', 300]]);

const serialize = (params) =>
  params ? `?${new URLSearchParams(Object.entries(params).reduce((a, [k, v]) => { a[k] = String(v); return a; }, {})).toString()}` : '';

const buildHeaders = (token, json = true) => {
  const h = new Headers();
  if (json) h.set('Content-Type', 'application/json');
  if (token) h.set('Authorization', `Bearer ${token}`);
  return h;
};

const getCacheOpt = (token, url) =>
  token ? { cache: 'no-store' } : (urlCache.get(url) ?? DEFAULT_CACHE_SECONDS) > 0
    ? { next: { revalidate: urlCache.get(url) ?? DEFAULT_CACHE_SECONDS } }
    : { cache: 'no-store' };

const handle = async (res) => {
  const data = await res.json().catch(() => null);
  if (!res.ok) throw new Error((data || {}).message ?? res.statusText);
  if (data && typeof data === 'object' && 'success' in data) {
    if (data.success) return data;
    throw new Error(data.message ?? 'API error');
  }
  return data;
};

export const serverApi = {
  get: async (url, params) => {
    const session = await getServerSession(authOptions);
    const res = await fetch(
      `${BASE_URL}${url}${serialize(params)}`,
      { method: 'GET', headers: buildHeaders(session?.accessToken), ...getCacheOpt(session?.accessToken, url) },
    );
    return handle(res);
  },

  post: async (url, data) => {
    const session = await getServerSession(authOptions);
    const res = await fetch(
      `${BASE_URL}${url}`,
      { method: 'POST', headers: buildHeaders(session?.accessToken), body: JSON.stringify(data), cache: 'no-store' },
    );
    return handle(res);
  },

  put: async (url, data) => {
    const session = await getServerSession(authOptions);
    const res = await fetch(
      `${BASE_URL}${url}`,
      { method: 'PUT', headers: buildHeaders(session?.accessToken), body: JSON.stringify(data), cache: 'no-store' },
    );
    return handle(res);
  },

  patch: async (url, data) => {
    const session = await getServerSession(authOptions);
    const res = await fetch(
      `${BASE_URL}${url}`,
      { method: 'PATCH', headers: buildHeaders(session?.accessToken), body: JSON.stringify(data), cache: 'no-store' },
    );
    return handle(res);
  },

  delete: async (url) => {
    const session = await getServerSession(authOptions);
    const res = await fetch(
      `${BASE_URL}${url}`,
      { method: 'DELETE', headers: buildHeaders(session?.accessToken), cache: 'no-store' },
    );
    return handle(res);
  },

  postFormData: async (url, formData) => {
    const session = await getServerSession(authOptions);
    const res = await fetch(
      `${BASE_URL}${url}`,
      { method: 'POST', headers: buildHeaders(session?.accessToken, false), body: formData, cache: 'no-store' },
    );
    return handle(res);
  },
};

export const getCompanyInfo = () => serverApi.get('/companyinfo/active');
