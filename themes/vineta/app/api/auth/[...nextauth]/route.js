import NextAuth from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        try {
          // Customer login API call
          const response = await fetch('http://localhost:33800/customer/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          })

          const result = await response.json()

          if (response.ok && result.isSuccessful && result.data?.isSuccessful) {
            const customerData = result.data
            return {
              id: customerData.customer.id,
              email: customerData.customer.email,
              name: customerData.customer.nameSurname,
              accessToken: customerData.token,
              customer: customerData.customer
            }
          }

          // Login failed
          return null
        } catch (error) {
          console.error('Customer login error:', error)
          return null
        }
      }
    })
  ],
  pages: {
    signIn: '/login',
    signUp: '/register'
  },
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours (same as backend token)
  },
  callbacks: {
    async jwt({ token, user }) {
      // Initial sign in
      if (user) {
        token.id = user.id
        token.email = user.email
        token.name = user.name
        token.accessToken = user.accessToken
        token.customer = user.customer
      }

      // Validate token on each request
      if (token.accessToken) {
        try {
          const response = await fetch('http://localhost:33800/customer/validate', {
            headers: {
              'Authorization': `Bearer ${token.accessToken}`,
            },
          })

          if (!response.ok) {
            // Token is invalid, clear session
            return {}
          }

          const result = await response.json()
          if (result.isSuccessful && result.data) {
            // Update customer data from validation response
            token.customer = result.data
          }
        } catch (error) {
          console.error('Token validation error:', error)
          // Clear session on validation error
          return {}
        }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id
        session.user.email = token.email
        session.user.name = token.name
        session.accessToken = token.accessToken
        session.customer = token.customer
      }
      return session
    }
  },
  secret: process.env.NEXTAUTH_SECRET,
})

export { handler as GET, handler as POST }
