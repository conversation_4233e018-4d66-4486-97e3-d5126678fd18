import Banner from "@/components/homes/Banner";
import Banner2 from "@/components/homes/Banner2";
import Categories from "@/components/homes/Categories";
import Hero from "@/components/homes/Hero";
import Products from "@/components/homes/Products";
import Newsletter from "@/components/modals/Newsletter";
import Cookie from "@/components/modals/Cookie";
import React from "react";
// Data Examples
// import { categories } from "@/data/categories";
import { products11 as products } from "@/data/products";
import { slides } from "@/data/home/<USER>";
import { banner } from "@/data/home/<USER>";
import { banner2 } from "@/data/home/<USER>";
import { getCategories } from "@/services/category";
export const metadata = {
  title: "Anasayfa | Dermedic",
  description: "Dermedic - Hassas Ciltler İçin Rahatlama",
};
async function getCategoriesResponse() {
  const categoryResponse = await getCategories();
  return categoryResponse.map((category) => {
    return {
      categoryName: category.name,
      slug: category.slug,
      altText: category.description,
      imgSrc: category.image
    }
  });
}
export default async function page() {
  let categories = await getCategoriesResponse();
  return (
    <>
      <div className="font-2 bg-surface-5">

        <Hero slides={slides} />
        {/* <Marquee /> */}
        <Categories categories={categories} />
        <Products products={products} />
        <Banner items={banner} />
        <Banner2 item={banner2} />
        <Newsletter />
        <Cookie />
      </div>
    </>
  );
}
