import ShopCart from "@/components/otherPages/ShopCart";
import React from "react";
import Link from "next/link";
import Breadcumb from "@/components/common/Breadcumb";


/**
 * @typedef {Object} CartProduct
 * @property {number} id - Ürün ID'si
 * @property {string} title - <PERSON>r<PERSON><PERSON> b<PERSON>
 * @property {string} imgSrc - Ürün görsel URL'i
 * @property {number} price - Ürün fiyatı
 * @property {number} quantity - Sepetteki ürün adedi
 * @property {string} [variant] - <PERSON>rü<PERSON> varyantı (Renk/Beden) (Opsiyonel)
 */

/**
 * @typedef {Object} ShippingEstimate
 * @property {string} country - Ülke
 * @property {string} state - Şehir/Bölge
 * @property {string} zipCode - Posta kodu
 */

/**
 * @typedef {Object} PaymentMethod
 * @property {string} id - Ödeme yöntemi ID'si
 * @property {string} name - Ödeme yöntemi adı (visa, mastercard vs.)
 * @property {string} icon - Ödeme yöntemi icon SVG'si
 */

/**
 * @typedef {Object} CartTotals
 * @property {number} subtotal - Ara toplam
 * @property {number} shipping - Kargo ücreti
 * @property {number} discount - İndirim tutarı
 * @property {number} total - Genel toplam
 */

/**
 * @typedef {Object} CartContext
 * @property {CartProduct[]} cartProducts - Sepetteki ürünler
 * @property {function} setCartProducts - Sepet ürünlerini güncelleme fonksiyonu
 * @property {function} updateQuantity - Ürün adedi güncelleme fonksiyonu
 * @property {function} removeItem - Ürün silme fonksiyonu
 * @property {CartTotals} totals - Sepet toplamları
 */


/**
 * Sepet içeriğini getir
 * @returns {Promise<CartProduct[]>}
 */
export const getCartItems = async () => {
  return api.get('/cart/items');
};

/**
 * Kargo bilgilerini hesapla
 * @param {ShippingEstimate} shippingData
 * @returns {Promise<{cost: number, estimatedDays: number}>}
 */
export const calculateShipping = async (shippingData) => {
  return api.post('/cart/shipping-estimate', shippingData);
};

/**
 * İndirim kuponu uygula
 * @param {string} couponCode
 * @returns {Promise<{isValid: boolean, discount: number, message: string}>}
 */
export const applyCoupon = async (couponCode) => {
  return api.post('/cart/apply-coupon', { code: couponCode });
};

/**
 * Varsayılan sepet durumu
 * @type {CartContext}
 */


export const metadata = {
  title: "Sepetiniz || Vineta - Multipurpose React Nextjs eCommerce",
  description: "Sepetiniz || Dermedic",
};
export default function page() {
  return (
    <>
      <>
        <Breadcumb pageName="Cart" pageTitle="Sepetiniz" />

        {/* /Title Page */}
        <div className="flat-spacing-24">
          <div className="container">
            <div className="row justify-content-center">
              <div className="col-xl-4 col-sm-8">
                <div className="tf-cart-head text-center">
                  <p className="text-xl-3 title text-dark-4">
                    Spend <span className="fw-medium">$100</span> more to get
                    <span className="fw-medium">Free Shipping</span>
                  </p>
                  <div className="progress-sold tf-progress-ship">
                    <div
                      className="value"
                      style={{ width: "60%" }}
                      data-progress={60}
                    >
                      <i className="icon icon-car" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
      <ShopCart />
    </>
  );
}
