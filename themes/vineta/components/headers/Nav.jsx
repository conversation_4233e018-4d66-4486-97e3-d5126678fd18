"use client";
import Link from "next/link";
import React from "react";
import { usePathname } from "next/navigation";

export default function Nav({ productsMenu }) {
  const pathname = usePathname();
  const isMenuActive = (link) => {
    return link.slug?.split("/")[1] == pathname.split("/")[1];
  };
  const isMenuParentActive = (menu) => {
    return menu && Array.isArray(menu) ? menu.some((elm) => isMenuActive(elm)) : false;
  };
  const isMenuParentActive2 = (menu) => {
    return menu && Array.isArray(menu) ? menu.some((elm) => isMenuParentActive(elm.products)) : false;
  };

  return (
    <>
      {" "}
      <li className="menu-item">
        <a href="/" className="item-link">
          Ana Sayfa
        </a>
      </li>
      <li className="menu-item">
        <a href="/urunler" className={`item-link ${isMenuParentActive2(productsMenu) ? "menuActive" : ""} `}  >
          Ürünler
          <i className="icon icon-arr-down" />
        </a>
        <div className="sub-menu mega-menu mega-product">
          <div className="wrapper-sub-menu" style={{ width: "100%", gridTemplateColumns: "repeat(5, 1fr)" }}>
            {productsMenu && Array.isArray(productsMenu) ? productsMenu.map((menuItem, index) => (
              <div className="mega-menu-item" key={index}>
                <div className="menu-heading">{menuItem.categoryName}</div>
                <ul className="menu-list">
                  {menuItem.products && Array.isArray(menuItem.products) ? menuItem.products.map((product, index) => (
                    <li key={index}>
                      <Link href={`/${product.slug}`} className={`menu-link-text link  ${isMenuActive(product) ? "menuActive" : ""} `}  >
                        {product.name}
                      </Link>
                    </li>
                  )) : null}
                </ul>
              </div>
            )) : null}
          </div>
        </div>
      </li>

      {/* <li className="menu-item"> */}
      {/*   <a href="/campaigns" className="item-link"> */}
      {/*     Campaigns */}
      {/*   </a> */}
      {/* </li> */}
    </>
  );
}
