"use client";
import React from "react";
import { Autoplay, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import Link from "next/link";
import Image from "next/image";
export default function Hero({ slides }) {
  return (
    <section className="tf-slideshow slider-skincare slider-default">
      <Swiper
        className="swiper tf-sw-slideshow slider-effect-fade"
        modules={[Autoplay, Pagination]}
        autoplay={{
          delay: 3000,
        }}
        speed={2500}
        loop
        pagination={{
          clickable: true,
          el: ".spdh2",
        }}
        dir="ltr"
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index} className="swiper-slide">
            <div className="slider-wrap">
              <div className="image">
                <Image
                  src={slide.imgSrc}
                  alt=""
                  className="lazyload"
                  width={1920}
                  height={945}
                />
              </div>
              <div className="box-content">
                <div className="container">
                  <div className="content-slider text-left">
                    <div className="box-title-slider">
                      <h3 className="sub display-xl-1 text-white fade-item fade-item-2" dangerouslySetInnerHTML={{ __html: slide.title }}>
                      </h3>
                      <h2 className="heading display-xl-2 text-white fw-medium fade-item fade-item-1 ">
                        {slide.subTitle}
                      </h2>

                    </div>
                    <div className="box-btn-slider fade-item fade-item-3">
                      <Link
                        href={`/${slide.slug}`}
                        className="tf-btn btn-white animate-btn animate-dark"
                      >
                        {slide.btnText}
                        <i className="icon icon-arr-right" />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
        <div className="wrap-pagination">
          <div className="container">
            <div className="sw-dots sw-pagination-slider justify-content-center spdh2" />
          </div>
        </div>
      </Swiper>
    </section>
  );
}
