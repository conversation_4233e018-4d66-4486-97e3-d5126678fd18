import React from "react";

export default function AdditionalInfo({ product }) {
  // Ürün bilgilerinden ek bilgileri çıkar
  const additionalInfo = [
    { label: "SKU", value: product?.sku },
    { label: "<PERSON><PERSON>", value: product?.brand },
    { label: "<PERSON><PERSON><PERSON>", value: product?.categoryName },
    { label: "Stok Durumu", value: product?.inStock ? "Stokta Var" : "Stokta Yok" },
    {
      label: "Renkler",
      value: product?.colors?.length > 0 ? product.colors.map(c => c.display || c.label).join(", ") : null
    },
    {
      label: "Boyutlar",
      value: product?.sizes?.length > 0 ? product.sizes.map(s => s.display || s.label).join(", ") : null
    },
  ].filter(item => item.value); // Sadece değeri olan bilgileri göster

  if (additionalInfo.length === 0) {
    return (
      <div className="text-muted">
        <p><PERSON>u ürün için ek bilgi bulunmuyor.</p>
      </div>
    );
  }

  return (
    <table className="tb-info-product text-md">
      <tbody>
        {additionalInfo.map((info, index) => (
          <tr key={index} className="tb-attr-item">
            <th className="tb-attr-label">{info.label}</th>
            <td className="tb-attr-value">
              <p>{info.value}</p>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
