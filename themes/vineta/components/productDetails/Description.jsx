import React from "react";

export default function Description({ product }) {
  // Eğer product description yoksa varsayılan mesaj g<PERSON>
  if (!product?.description) {
    return (
      <div className="item">
        <p className="text-muted"><PERSON><PERSON> <PERSON>r<PERSON>n için henüz açıklama eklenmemiş.</p>
      </div>
    );
  }
  product.description = product.description.replace(/\n/g, '<br>');

  return (
    <div className="item">
      <div
        className="product-description"
        dangerouslySetInnerHTML={{ __html: product.description }}
      />
    </div>
  );
}
