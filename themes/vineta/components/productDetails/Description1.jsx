import React from "react";
import Description from "./Description";
import Material from "./Material";
import ReturnPolicies from "./ReturnPolicies";
import AdditionalInfo from "./AdditionalInfo";
import ProductAttributes from "./ProductAttributes";
import Reviews from "./Reviews";

export default function Description1({ product }) {
  return (
    <section className="flat-spacing pt-0">
      <div className="container">
        <div className="widget-accordion wd-product-descriptions">
          <div
            className="accordion-title collapsed"
            data-bs-target="#description"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="description"
            role="button"
          >
            <span>Açıklama</span>
            <span className="icon icon-arrow-down" />
          </div>
          <div id="description" className="collapse">
            <div className="accordion-body widget-desc">
              <Description product={product} />
            </div>
          </div>
        </div>

        {/*         <div className="widget-accordion wd-product-descriptions">
          <div
            className="accordion-title collapsed"
            data-bs-target="#material"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="material"
            role="button"
          >
            <span>Materials</span>
            <span className="icon icon-arrow-down" />
          </div>
          <div id="material" className="collapse">
            <div className="accordion-body widget-material">
              <Material />
            </div>
          </div>
        </div> */}

        {/*         
        <div className="widget-accordion wd-product-descriptions">
          <div
            className="accordion-title collapsed"
            data-bs-target="#returnPolicies"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="returnPolicies"
            role="button"
          >
            <span>Return Policies</span>
            <span className="icon icon-arrow-down" />
          </div>
          <div id="returnPolicies" className="collapse">
            <div className="accordion-body">
              <ReturnPolicies />
            </div>
          </div>
        </div> 
        */}
        {/* Additional Information */}
        <div className="widget-accordion wd-product-descriptions">
          <div
            className="accordion-title collapsed"
            data-bs-target="#additionalInfo"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="additionalInfo"
            role="button"
          >
            <span>Ek Bilgiler</span>
            <span className="icon icon-arrow-down" />
          </div>
          <div id="additionalInfo" className="collapse">
            <div className="accordion-body">
              <AdditionalInfo product={product} />
            </div>
          </div>
        </div>
        {/* Product Attributes */}
        <div className="widget-accordion wd-product-descriptions">
          <div
            className="accordion-title collapsed"
            data-bs-target="#productAttributes"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="productAttributes"
            role="button"
          >
            <span>Özellikler</span>
            <span className="icon icon-arrow-down" />
          </div>
          <div id="productAttributes" className="collapse">
            <div className="accordion-body">
              <ProductAttributes product={product} />
            </div>
          </div>
        </div>
        {/* Reviews */}
        {/* <div className="widget-accordion wd-product-descriptions"> */}
        {/*   <div */}
        {/*     className="accordion-title collapsed" */}
        {/*     data-bs-target="#reviews" */}
        {/*     data-bs-toggle="collapse" */}
        {/*     aria-expanded="true" */}
        {/*     aria-controls="reviews" */}
        {/*     role="button" */}
        {/*   > */}
        {/*     <span>Yorumlar</span> */}
        {/*     <span className="icon icon-arrow-down" /> */}
        {/*   </div> */}
        {/*   <div id="reviews" className="collapse"> */}
        {/*     <div className="accordion-body wd-customer-review"> */}
        {/*       <Reviews reviews={product?.reviews || []} /> */}
        {/*     </div> */}
        {/*   </div> */}
        {/* </div> */}
      </div>
    </section>
  );
}
