"use client";
import React from "react";
import Link from "next/link";

export default function Breadcumb({ product, breadcrumb }) {
  // Backend'den gelen breadcrumb data's<PERSON><PERSON><PERSON> kullan, yoksa product bilgilerinden oluştur
  const breadcrumbItems = breadcrumb || [
    { name: "<PERSON>", url: "/" },
    { name: "<PERSON>ü<PERSON> Ürü<PERSON>", url: "/urunler" },
    ...(product?.categoryName && product?.categorySlug ?
      [{ name: product.categoryName, url: `/urunler/${product.categorySlug}` }] : []
    ),
    { name: product?.title || "Ürün Detayı", url: null, current: true }
  ];

  return (
    <>
      <div className="breadcrumb-sec">
        <div className="container">
          <div className="breadcrumb-wrap">
            <div className="breadcrumb-list">
              {breadcrumbItems.map((item, index) => (
                <React.Fragment key={index}>
                  {index > 0 && (
                    <div className="breadcrumb-item dot">
                      <span />
                    </div>
                  )}
                  {item.current ? (
                    <div className="breadcrumb-item current">
                      {item.name}
                    </div>
                  ) : (
                    <Link href={item.url} className="breadcrumb-item">
                      {item.name}
                    </Link>
                  )}
                </React.Fragment>
              ))}
            </div>
            {/* <div className="breadcrumb-prev-next"> */}
            {/*   <a href="#" className="breadcrumb-prev" onClick={(e) => e.preventDefault()}> */}
            {/*     <i className="icon icon-arr-left" /> */}
            {/*   </a> */}
            {/*   <Link href={`/products`} className="breadcrumb-back"> */}
            {/*     <i className="icon icon-shop" /> */}
            {/*   </Link> */}
            {/*   <a href="#" className="breadcrumb-next" onClick={(e) => e.preventDefault()}> */}
            {/*     <i className="icon icon-arr-right2" /> */}
            {/*   </a> */}
            {/* </div> */}
          </div>
        </div>
      </div>
    </>
  );
}
