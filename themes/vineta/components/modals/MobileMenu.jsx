"use client";
import Link from "next/link";
import React from "react";
import { useState, useEffect } from "react";
import { headerData } from "@/data/header-products";
import { usePathname } from "next/navigation";
import { getCategories } from "@/services/category";
import { injectCategories } from "@/utils/injectCategories";

export default function MobileMenu() {
  const pathname = usePathname();
  const [headerNavData, setProductsData] = useState(headerData);
  /* console.log("Current Pathname:", pathname); */
  useEffect(() => {
    let isMounted = true;

    const categoryData = async () => {
      const response = await getCategories();
      console.log(response)
      if (isMounted) {
        setProductsData(prev => injectCategories(prev, response));
      }
    };
    categoryData();


  }, [])

  const isMenuActive = (link) => {
    return link.slug?.split("/")[1] == pathname.split("/")[1];
  };
  const isMenuParentActive = (menu) => {
    return menu.some((elm) => isMenuActive(elm));
  };
  const isMenuParentActive2 = (menu) => {
    return menu.some((elm) => isMenuParentActive(elm.products));
  };
  return (
    <div className="offcanvas offcanvas-start canvas-mb" id="mobileMenu">
      <button
        className="icon-close icon-close-popup"
        data-bs-dismiss="offcanvas"
        aria-label="Close"
      />
      <div className="mb-canvas-content">
        <div className="mb-body">
          <div className="mb-content-top">
            <form className="form-search">
              <input
                type="text"
                placeholder="Search product"
                className=""
                name="text"
                tabIndex={0}
                defaultValue=""
                aria-required="true"
                required
              />
              <button type="submit">
                <i className="icon icon-search" />
              </button>
            </form>
            <ul className="nav-ul-mb" id="wrapper-menu-navigation">
              <li className="nav-mb-item">
                <a href="/" className="mb-menu-link"  >
                  Anasayfa
                </a>
              </li>

              {headerNavData.productsMenu.map((menuItem, i) =>
              (
                <li className="nav-mb-item" key={i}>
                  <a
                    href={`#sub-product-layout${i}`}
                    className={`collapsed mb-menu-link ${isMenuParentActive(menuItem.products) ? "menuActive" : ""
                      } `}
                    data-bs-toggle="collapse"
                    aria-expanded="true"
                    aria-controls={`#sub-product-layout${i}`}
                  >
                    <span>{menuItem.categoryName}</span>
                    <span className="btn-open-sub" />
                  </a>
                  <div id={`sub-product-layout${i}`} className="collapse">
                    <ul className="sub-nav-menu">
                      {menuItem.products.map((product, i) => (
                        <li key={i}>  <Link href={`${product.slug}`} className={`sub-nav-link  ${isMenuActive(product) ? "menuActive" : ""}`}  >
                          {product.name}
                        </Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                </li>
              )
              )}
            </ul>

          </div>

          <div className="mb-other-content">
            <div className="group-icon">
              <Link href={`/account/wish-list`} className="site-nav-icon">
                <i className="icon icon-heart" />
                İstek Listem
              </Link>
              <a
                href="#login"
                data-bs-toggle="offcanvas"
                className="site-nav-icon"
              >
                <i className="icon icon-user" />
                Giriş Yap
              </a>
            </div>
            <div className="mb-notice">
              <Link href={`/iletisim`} className="text-need">
                İletişim
              </Link>
            </div>
            {/* <div className="mb-contact"> */}
            {/*   <p>Address: 123 Yarran st, Punchbowl, NSW 2196, Australia</p> */}
            {/* </div> */}
            <ul className="mb-info">
              <li>
                Email: <b className="fw-medium">{headerNavData.contact.email}</b>
              </li>
              {/* <li> */}
              {/*   Phone: <b className="fw-medium">**************</b> */}
              {/* </li> */}
            </ul>
          </div>
        </div>
        {/* <div className="mb-bottom"> */}
        {/*   <div className="bottom-bar-language"> */}
        {/*     <div className="tf-languages"> */}
        {/*       <LanguageSelect parentClassName="image-select center style-default type-languages" /> */}
        {/*     </div> */}
        {/*   </div> */}
        {/* </div> */}
      </div>
    </div>
  );
}
