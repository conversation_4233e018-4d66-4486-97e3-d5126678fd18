import React from 'react';
import { generateProductStructuredData, generateBreadcrumbStructuredData } from '@/lib/seo/metadata';

/**
 * Structured Data (JSON-LD) Component
 * SEO için structured data ekleme
 */

// Ürün structured data component'i
export function ProductStructuredData({ product }) {
  if (!product) return null;

  const structuredData = generateProductStructuredData(product);
  
  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

// Breadcrumb structured data component'i
export function BreadcrumbStructuredData({ breadcrumbs }) {
  if (!breadcrumbs || breadcrumbs.length === 0) return null;

  const structuredData = generateBreadcrumbStructuredData(breadcrumbs);
  
  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

// Organization structured data
export function OrganizationStructuredData() {
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Vineta',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com',
    logo: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com'}/images/logo.png`,
    description: 'Premium alışveriş deneyimi sunan online mağaza',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+90-XXX-XXX-XXXX',
      contactType: 'customer service',
      availableLanguage: 'Turkish',
    },
    sameAs: [
      'https://www.facebook.com/vineta',
      'https://www.instagram.com/vineta',
      'https://www.twitter.com/vineta',
    ],
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationData),
      }}
    />
  );
}

// Website structured data
export function WebsiteStructuredData() {
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Vineta',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com',
    description: 'Premium alışveriş deneyimi sunan online mağaza',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com'}/products?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(websiteData),
      }}
    />
  );
}

// E-commerce site structured data
export function EcommerceSiteStructuredData() {
  const ecommerceData = {
    '@context': 'https://schema.org',
    '@type': 'OnlineStore',
    name: 'Vineta',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com',
    description: 'Premium alışveriş deneyimi sunan online mağaza',
    currenciesAccepted: 'TRY',
    paymentAccepted: ['Credit Card', 'Debit Card', 'Bank Transfer'],
    priceRange: '₺₺',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'TR',
      addressLocality: 'İstanbul',
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(ecommerceData),
      }}
    />
  );
}

// Category page structured data
export function CategoryStructuredData({ category, products = [] }) {
  if (!category) return null;

  const categoryData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: category.name,
    description: `${category.name} kategorisindeki ürünleri keşfedin`,
    url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com'}/kategoriler/${category.slug}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: products.length,
      itemListElement: products.slice(0, 10).map((product, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Product',
          name: product.title,
          url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com'}/products/${product.slug}`,
          image: product.imgSrc,
          offers: {
            '@type': 'Offer',
            price: product.price,
            priceCurrency: 'TRY',
            availability: product.inStock 
              ? 'https://schema.org/InStock' 
              : 'https://schema.org/OutOfStock',
          },
        },
      })),
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(categoryData),
      }}
    />
  );
}

// Product list page structured data
export function ProductListStructuredData({ products = [], pageType = 'products' }) {
  if (!products || products.length === 0) return null;

  const pageNames = {
    products: 'Tüm Ürünler',
    'new-season': 'Yeni Sezon Ürünleri',
    discounted: 'İndirimli Ürünler',
  };

  const listData = {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    name: pageNames[pageType] || 'Ürünler',
    numberOfItems: products.length,
    itemListElement: products.slice(0, 20).map((product, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@type': 'Product',
        name: product.title,
        url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com'}/products/${product.slug}`,
        image: product.imgSrc,
        brand: product.brand ? {
          '@type': 'Brand',
          name: product.brand,
        } : undefined,
        offers: {
          '@type': 'Offer',
          price: product.price,
          priceCurrency: 'TRY',
          availability: product.inStock 
            ? 'https://schema.org/InStock' 
            : 'https://schema.org/OutOfStock',
        },
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(listData),
      }}
    />
  );
}

// FAQ structured data (ürün sayfaları için)
export function FAQStructuredData({ faqs = [] }) {
  if (!faqs || faqs.length === 0) return null;

  const faqData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(faqData),
      }}
    />
  );
}

// Review structured data
export function ReviewStructuredData({ reviews = [], product }) {
  if (!reviews || reviews.length === 0 || !product) return null;

  const reviewData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.title,
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: product.reviewRate || 0,
      reviewCount: product.reviewCount || 0,
      bestRating: 5,
      worstRating: 1,
    },
    review: reviews.slice(0, 10).map(review => ({
      '@type': 'Review',
      author: {
        '@type': 'Person',
        name: review.customerName || 'Anonim',
      },
      reviewRating: {
        '@type': 'Rating',
        ratingValue: review.rating,
        bestRating: 5,
        worstRating: 1,
      },
      reviewBody: review.comment,
      datePublished: review.createdAt,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(reviewData),
      }}
    />
  );
}
