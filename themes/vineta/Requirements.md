## Phase 1

    - [x] <PERSON><PERSON><PERSON><PERSON>
    - [] <PERSON><PERSON><PERSON><PERSON>
    - [x] Filtreler
    - [] <PERSON>r<PERSON><PERSON>

    - [ ] Anasayfanın düzenlenmesi
    - [x] Ürünlerin listelenmesi
    - [ ] Ürün listeleme kategori sayfası
    - [x] Ürün listeleme filtreleri
    - [ ] <PERSON>rün listeleme sıralamalar
    - [ ] <PERSON>rün detay sayfası  

    Hatalar : 
    - [x] Ürün listeleme ekranında tekli list yapısı düzenlenecek
    - [ ] Ürün detay sayfası düzenlenecek

 Unutma!

    - [] Header 5'te white logo olmadığı için filtre eklendi | invert(100%)
    - [] Markdown description'a markdown eklenecek
    - [x] Anasayfa'da logonun boyutu 128px'den 200px'e çıkarılacak ==> .logo-header img {width: 200px;} ==> _header.scss dosyasında 531. satırda ilgili değişiklik yapıldı
    - [] API'lerin gelmesi sonrası sizeSelect'e düzenle

    - [ ] ProductHeadings'i düzenle
    - [ ] Shipping sayfası oluşturulacak
    - [ ] Quickview'u düzenlemyi unutma
    - [ ] Rounting yapısını düzenle ==> /products/[categorySlug]/[productSlug] şeklinde olmalı
    - [ ] Newsletter'daki vercel post'u unutma

## Parametrik Hale Getirilecekler

    - [ ] wish-list
    - [ ] 

## Phase 2

    - [ ] Kullanıcı Girişi
    - [ ] Kullanıcı parola değiştirme    
    - [ ] Kullanıcı adres ekleme, çıkarma, değiştirme, silme
    - [ ] Kullanıcı sipariş ekranı
    - [ ] Kullanıcı sipariş iptali
    - [ ] Ürünü sepete ekleme, çıkarma, eksiltme
    - [ ] Ürünü istek listesine ekleme
    - [ ] Kullanıcı ayarları sayfası - profil fotoğrafı, email değişikliği, telefon değişikliği, parola değişikliği
    - [ ] Ödeme altyapısı
    - [ ] Kargo entegrasyonu
    - [ ] Resources
    - [ ] Hakkımızda --> Dermedic'e yönlendirilecek
    - [ ] İletişim --> Dermedic'e yönlendirilebilir
    - [ ] Privacy Policies
    - [ ] Return & Refunds
    - [ ] Shipping

## Eksikler

    - [ ] Kategorilere görsel eklenecek
    - [ ] Kategorilere description eklenecek ancak metadata
    - [ ] Review DTO'sunda kullanıcı görseli eklenecek
    - [ ] Adreslerde adresin fatura adresi mi yoksa teslimat adresi mi olacağını belirten değişken yok
    - [ ] Brand için slug yok
    

# --------------------------------------

# Notes

# h1 başlığı (En Büyük)

## h2 başlığı

### h3 başlığı

#### h4 başlığı

##### h5 başlığı

###### h6 başlığı (En Küçük)

**Bu kalın bir yazıdır**
***Bu italic bir yazıdır***
~~Bu üstü çizili bir yazıdır.~~

- Eleman 1
- Eleman 2

- [x] Yapıldı
- [ ] Yapılmadı

 > Bu bir alıntı yazısıdır.

 ```
    .font-bold {
     font-weight: bold;
    }
```

#### Tablo Elemanı

| Ad | Soyad |
|--|--|
| Kemal | Mutlu |
