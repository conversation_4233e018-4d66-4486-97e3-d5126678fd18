import { api } from '@/lib/api/client';
import { serverApi } from '@/lib/api/server';

/**
 * B2C Customer Category API Services
 * Server-side rendering ve SEO odaklı kategori servisleri
 */

// Tüm kategorileri getir
export const getCategories = async () => {
  try {
    return await api.get('/category');
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

// Server-side rendering için tüm kategoriler
export const getCategoriesSSR = async () => {
  try {
    return await serverApi.get('/category');
  } catch (error) {
    console.error('Error fetching categories (SSR):', error);
    throw error;
  }
};

// Ana kategorileri getir
export const getMainCategories = async () => {
  try {
    return await api.get('/category/main');
  } catch (error) {
    console.error('Error fetching main categories:', error);
    throw error;
  }
};

// Server-side rendering için ana kategoriler
export const getMainCategoriesSSR = async () => {
  try {
    return await serverApi.get('/category/main');
  } catch (error) {
    console.error('Error fetching main categories (SSR):', error);
    throw error;
  }
};

// Kategori hiyerarşisini getir
export const getCategoryHierarchy = async () => {
  try {
    return await api.get('/category/hierarchy');
  } catch (error) {
    console.error('Error fetching category hierarchy:', error);
    throw error;
  }
};

// Server-side rendering için kategori hiyerarşisi
export const getCategoryHierarchySSR = async () => {
  try {
    return await serverApi.get('/category/hierarchy');
  } catch (error) {
    console.error('Error fetching category hierarchy (SSR):', error);
    throw error;
  }
};

// Slug ile kategori detayı getir
export const getCategoryBySlug = async (slug) => {
  try {
    return await api.get(`/category/${slug}`);
  } catch (error) {
    console.error('Error fetching category by slug:', error);
    throw error;
  }
};

// Server-side rendering için kategori detayı
export const getCategoryBySlugSSR = async (slug) => {
  try {
    return await serverApi.get(`/category/${slug}`);
  } catch (error) {
    console.error('Error fetching category by slug (SSR):', error);
    throw error;
  }
};

// Kategoriye ait ürünleri getir
export const getCategoryProducts = async (slug, filters = {}) => {
  try {
    return await api.get(`/category/${slug}/products`, filters);
  } catch (error) {
    console.error('Error fetching category products:', error);
    throw error;
  }
};

// Server-side rendering için kategori ürünleri
export const getCategoryProductsSSR = async (slug, filters = {}) => {
  try {
    return await serverApi.get(`/category/${slug}/products`, buildFilterParams(filters));
  } catch (error) {
    console.error('Error fetching category products (SSR):', error);
    throw error;
  }
};

// Alt kategorileri getir (parent ID ile)
export const getSubCategories = async (parentId) => {
  try {
    return await api.get(`/category/${parentId}/subcategories`);
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    throw error;
  }
};

// Kategori breadcrumb'ını getir
export const getCategoryBreadcrumb = async (slug) => {
  try {
    return await api.get(`/category/${slug}/breadcrumb`);
  } catch (error) {
    console.error('Error fetching category breadcrumb:', error);
    throw error;
  }
};

// Server-side rendering için kategori breadcrumb
export const getCategoryBreadcrumbSSR = async (slug) => {
  try {
    return await serverApi.get(`/api/category/${slug}/breadcrumb`);
  } catch (error) {
    console.error('Error fetching category breadcrumb (SSR):', error);
    throw error;
  }
};

// Kardeş kategorileri getir
export const getSiblingCategories = async (slug) => {
  try {
    return await api.get(`/api/category/${slug}/siblings`);
  } catch (error) {
    console.error('Error fetching sibling categories:', error);
    throw error;
  }
};

// Alt kategorileri getir (slug ile)
export const getChildCategories = async (slug) => {
  try {
    return await api.get(`/category/${slug}/children`);
  } catch (error) {
    console.error('Error fetching child categories:', error);
    throw error;
  }
};

// Server-side rendering için alt kategoriler
export const getChildCategoriesSSR = async (slug) => {
  try {
    return await serverApi.get(`/category/${slug}/children`);
  } catch (error) {
    console.error('Error fetching child categories (SSR):', error);
    throw error;
  }
};

// Kategori SEO metadata'sını getir
export const getCategoryMetadata = async (slug) => {
  try {
    return await api.get(`/category/${slug}/metadata`);
  } catch (error) {
    console.error('Error fetching category metadata:', error);
    throw error;
  }
};

// Server-side rendering için kategori metadata
export const getCategoryMetadataSSR = async (slug) => {
  try {
    return await serverApi.get(`/category/${slug}/metadata`);
  } catch (error) {
    console.error('Error fetching category metadata (SSR):', error);
    throw error;
  }
};

// Popüler kategorileri getir
export const getPopularCategories = async (count = 10) => {
  try {
    return await api.get('/category/popular', { count });
  } catch (error) {
    console.error('Error fetching popular categories:', error);
    throw error;
  }
};

// Server-side rendering için popüler kategoriler
export const getPopularCategoriesSSR = async (count = 10) => {
  try {
    return await serverApi.get('/category/popular', { count });
  } catch (error) {
    console.error('Error fetching popular categories (SSR):', error);
    throw error;
  }
};

// Öne çıkan kategorileri getir
export const getFeaturedCategories = async (count = 6) => {
  try {
    return await api.get('/category/featured', { count });
  } catch (error) {
    console.error('Error fetching featured categories:', error);
    throw error;
  }
};

// Server-side rendering için öne çıkan kategoriler
export const getFeaturedCategoriesSSR = async (count = 6) => {
  try {
    return await serverApi.get('/category/featured', { count });
  } catch (error) {
    console.error('Error fetching featured categories (SSR):', error);
    throw error;
  }
};

// Kategori arama
export const searchCategories = async (query, count = 10) => {
  try {
    return await api.get('/category/search', { query, count });
  } catch (error) {
    console.error('Error searching categories:', error);
    throw error;
  }
};

// Kategori durumu kontrolü
export const getCategoryStatus = async (slug) => {
  try {
    return await api.get(`/category/${slug}/status`);
  } catch (error) {
    console.error('Error fetching category status:', error);
    throw error;
  }
};
const buildFilterParams = (filters) => {
  const params = {};

  // Sayfalama
  if (filters.page) params.page = filters.page;
  if (filters.pageSize) params.pageSize = filters.pageSize;
  if (filters.limit) params.limit = filters.limit;

  // Sıralama
  if (filters.sort) params.sort = filters.sort;
  if (filters.sortBy) params.sortBy = filters.sortBy;
  if (filters.sortOrder) params.sortOrder = filters.sortOrder;

  // Kategori filtreleri
  if (filters.category) {
    params.category = Array.isArray(filters.category)
      ? filters.category.join(',')
      : filters.category;
  }
  if (filters.categories) {
    params.categories = Array.isArray(filters.categories)
      ? filters.categories.join(',')
      : filters.categories;
  }

  // Marka filtreleri
  if (filters.brand) {
    params.brand = Array.isArray(filters.brand)
      ? filters.brand.join(',')
      : filters.brand;
  }
  if (filters.brands) {
    params.brands = Array.isArray(filters.brands)
      ? filters.brands.join(',')
      : filters.brands;
  }

  // // Boyut filtreleri
  // if (filters.size) {
  //   params.size = Array.isArray(filters.size)
  //     ? filters.size.join(',')
  //     : filters.size;
  // }
  // if (filters.sizes) {
  //   params.sizes = Array.isArray(filters.sizes)
  //     ? filters.sizes.join(',')
  //     : filters.sizes;
  // }

  // // Renk filtreleri
  // if (filters.color) {
  //   params.color = Array.isArray(filters.color)
  //     ? filters.color.join(',')
  //     : filters.color;
  // }
  // if (filters.colors) {
  //   params.colors = Array.isArray(filters.colors)
  //     ? filters.colors.join(',')
  //     : filters.colors;
  // }
  //
  // Fiyat filtreleri
  if (filters.priceMin || filters.price_min) params.priceMin = filters.priceMin || filters.price_min;
  if (filters.priceMax || filters.price_max) params.priceMax = filters.priceMax || filters.price_max;
  if (filters.priceRange) {
    if (Array.isArray(filters.priceRange) && filters.priceRange.length === 2) {
      params.priceMin = filters.priceRange[0];
      params.priceMax = filters.priceRange[1];
    }
  }

  // Stok durumu
  if ((filters.inStock !== undefined && filters.inStock !== null) || filters.in_stock !== undefined) {
    params.inStock = filters.inStock !== undefined ? filters.inStock : filters.in_stock;
  }

  // İndirim durumu
  if (filters.onSale !== undefined || filters.discounted !== undefined) {
    params.onSale = filters.onSale !== undefined ? filters.onSale : filters.discounted;
  }

  // Arama sorgusu
  if (filters.query || filters.search) {
    params.query = filters.query || filters.search;
  }

  // Sayfa türü (new-season, discounted, featured, vb.)
  if (filters.pageType) params.pageType = filters.pageType;

  // Rating filtresi
  if (filters.rating || filters.minRating) {
    params.minRating = filters.rating || filters.minRating;
  }

  return params;
};


