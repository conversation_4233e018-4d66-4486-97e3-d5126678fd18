import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

const intlMiddleware = createIntlMiddleware(routing);

export default async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Apply internationalization middleware
  const response = intlMiddleware(request);
  if (!pathname.includes("/admin") && !pathname.includes("/login")) {
    const url = new URL('/tr/login', request.url);
    return NextResponse.redirect(url);
  }
  // Check if the path is for the admin section
  if (pathname.includes("/admin")) {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, redirect to login page
    if (!token) {
      const url = new URL(`/login`, request.url);
      return NextResponse.redirect(url);
    }

    // If token exists but user is not an admin or developer, redirect to home page
    // This assumes your token has a 'roles' array
    const roles = token.roles as string[];
    if (!roles.includes("Admin") && !roles.includes("Developer")) {
      const url = new URL(`/`, request.url);
      return NextResponse.redirect(url);
    }
  }

  // For login page, if user is already authenticated, redirect to admin dashboard
  if (pathname.includes("/login")) {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    if (token) {
      const roles = token.roles as string[];

      // If user is an admin or developer, redirect to admin dashboard
      if (roles.includes("Admin") || roles.includes("Developer")) {
        const url = new URL(`/admin/dashboard`, request.url);
        return NextResponse.redirect(url);
      }

      // Otherwise redirect to home page
      const url = new URL(`/`, request.url);
      return NextResponse.redirect(url);
    }
  }

  return response;
}

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
};
