import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { hasServerPermission } from "@/lib/auth/server-permissions";
import { PermissionProvider } from "@/components/auth/permission-provider";
import CategoryListServer from "./components/CategoryListServer";
import PageHeaderServer from "../components/PageHeaderServer";

export default async function CategoriesPage() {
  // Server-side permission check - redirect if no access
  const canRead = await hasServerPermission("product_categories", "read");
  if (!canRead) {
    redirect("/admin/dashboard");
  }

  const t = await getTranslations("category");

  return (
    <div className="space-y-6">
      <PermissionProvider resource="product_categories">
        {({ read, create, update, delete: canDelete }) => (
          <>
            {/* Set page header using server component */}
            <PageHeaderServer
              title={t("title")}
              description={t("list")}
              actions={[
                {
                  actionLabel: t("add"),
                  actionUrl: "/admin/product-categories/add",
                  actionIcon: "add",
                  actionVariant: "default",
                  permissionResource: "product_categories",
                  permissionAction: "create",
                },
                {
                  actionLabel: t("productList"),
                  actionUrl: "/admin/products",
                  actionIcon: "list",
                  actionVariant: "link"

                }
              ]}
            />
            <CategoryListServer canRead={read} canCreate={create} canUpdate={update} canDelete={canDelete} />
          </>
        )}
      </PermissionProvider>
    </div>
  );
}
