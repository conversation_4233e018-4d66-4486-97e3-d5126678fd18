"use client"

import type React from "react"
import { useState, useC<PERSON>back } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
    Tag,
    BarChart,
    Settings,
    FileText,
    HelpCircle,
    Save,
    ArrowLeft,
} from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "next/navigation"
import { useCreateProduct, type ProductCreateDto } from "@/lib/api/hooks/useProducts"

// Import tab components
import { ProductAddDetailsTab } from "./tabs/ProductAddDetailsTab";
import { ProductAddAttributesTab } from "./tabs/ProductAddAttributesTab";
import { ProductAddVariantsTab } from "./tabs/ProductAddVariantsTab";
import { ProductAddSEOTab } from "./tabs/ProductAddSEOTab";
import { ProductAddFAQTab } from "./tabs/ProductAddFAQTab";
import type { ProductAttributeMappingCreateDto } from "@/lib/api/hooks/useProductAttributeMappings";

// Types
export type Productattribute = {
    id: string;
    name: string;
    value: string;
};

export type ProductVariant = {
    id: string;
    variantTypeId?: string;
    variantTypeName: string;
    optionId?: string;
    optionValue: string;
    sku: string;
    barcode?: string;
    stock: number;
    price: number;
    salePrice?: number;
    isActive: boolean;
    attributeCombination?: {
        attributeId: string;
        attributeValueId: string;
        attributeName: string;
        valueName: string;
    }[];
};

export type ProductFAQ = {
    id: string;
    question: string;
    answer: string;
};

export type ProductSEO = {
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
    structuredData?: string;
    canonicalUrl?: string;
    noIndex?: boolean;
    noFollow?: boolean;
};

export type ProductImage = {
    id: string;
    originalImagePath: string;
    thumbnailSmallPath: string;
    thumbnailMediumPath: string;
    isMainImage: boolean;
    sortOrder: number;
    altText: string;
};

export type ProductFormData = {
    name: string;
    sku: string;
    barcode: string;
    description: string;
    productType: number;
    categoryId: string;
    brandId: string;
    price: number;
    salePrice?: number;
    stock: number;
    status: string;
    images: ProductImage[];
    attributeMappings: ProductAttributeMappingCreateDto[];
    attributes: Productattribute[];
    variants: ProductVariant[];
    seo: ProductSEO;
    faq: ProductFAQ[];
};

export function ProductAddForm() {
    const t = useTranslations("product");
    const commonT = useTranslations("common");
    const router = useRouter();
    const createProductMutation = useCreateProduct();

    // Form state
    const [formData, setFormData] = useState<ProductFormData>({
        name: "",
        sku: "",
        barcode: "",
        description: "",
        productType: 0, // ProductType.Simple = 0
        categoryId: "",
        brandId: "",
        price: 0,
        salePrice: undefined,
        stock: 0,
        status: "active",
        images: [],
        attributeMappings: [],
        attributes: [],
        variants: [],
        seo: {
            metaTitle: "",
            metaDescription: "",
            metaKeywords: "",
            ogTitle: "",
            ogDescription: "",
            ogImage: "",
            structuredData: "",
            canonicalUrl: "",
            noIndex: false,
            noFollow: false,
        },
        faq: [],
    });

    const [isLoading, setIsLoading] = useState(false);

    // Update form data
    const updateFormData = useCallback((updates: Partial<ProductFormData>) => {
        setFormData((prev) => ({ ...prev, ...updates }));
    }, []);

    // Memoized update functions
    const updateAttributeMappings = useCallback(
        (attributeMappings: ProductAttributeMappingCreateDto[]) => {
            updateFormData({ attributeMappings });
        },
        [updateFormData]
    );

    // Handle form submission
    const handleSubmit = async () => {
        setIsLoading(true);
        try {
            // Prepare product data for API - backend'deki ProductCreateDto ile uyumlu
            const productData: ProductCreateDto = {
                name: formData.name,
                description: formData.description || undefined,
                productType: formData.productType,
                categoryId: formData.categoryId || undefined,
                brandId: formData.brandId || undefined,
                sku: formData.sku || undefined,
                barcode: formData.barcode || undefined,
                price: formData.price || undefined,
                stockQuantity: formData.stock || undefined,
                // Related data
                attributeMappings:
                    formData.attributeMappings.length > 0
                        ? formData.attributeMappings.map((mapping) => ({
                            attributeId: mapping.attributeId,
                            attributeValueId: mapping.attributeValueId,
                            // productId backend'te set edilecek
                        }))
                        : undefined,

                faqs:
                    formData.faq.length > 0
                        ? formData.faq.map((faq, index) => ({
                            question: faq.question,
                            answer: faq.answer,
                            sortOrder: index + 1,
                        }))
                        : undefined,
                seo: formData.seo.metaTitle || formData.seo.metaDescription || formData.seo.metaKeywords ? formData.seo : undefined,
            };

            // 1. First create the product
            const createdProduct = await createProductMutation.mutateAsync(productData);

            // 2. Then handle image uploads if there are images
            if (formData.images.length > 0) {
                const productSlug = formData.name.toLowerCase().replace(/\s+/g, "-");
                await handleImageUploads(formData.images, productSlug, createdProduct.productId);
            }

            // Redirect to products list
            router.push("/admin/products");
        } catch (error) {
            console.error("Error creating product:", error);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle image uploads - Now using MediaAPI
    const handleImageUploads = async (images: ProductImage[], productSlug: string, productId: string) => {
        console.log('Starting image uploads for', images.length, 'images');

        try {
            // Import the upload utility
            const { uploadImageToMediaAPI } = await import('@/lib/utils/fileUtils');
            const { prepareImageUpload } = await import('@/lib/utils/imageUtils');

            for (let i = 0; i < images.length; i++) {
                const image = images[i];
                console.log(`Uploading image ${i + 1}/${images.length}:`, image.originalImagePath);

                // Get the file from the blob URL
                const response = await fetch(image.originalImagePath);
                const blob = await response.blob();
                const file = new File([blob], `${productSlug}-${image.sortOrder}.jpg`, { type: blob.type });

                // Prepare upload data
                const uploadData = prepareImageUpload(
                    file,
                    productSlug,
                    image.sortOrder,
                    image.altText,
                    image.isMainImage
                );

                // Upload to MediaAPI
                const result = await uploadImageToMediaAPI(productId, uploadData);

                if (result.success) {
                    console.log(`Image ${i + 1} uploaded successfully:`, result.imageId);
                } else {
                    console.error(`Failed to upload image ${i + 1}:`, result.message);
                    throw new Error(result.message || 'Image upload failed');
                }

                // Clean up blob URL
                URL.revokeObjectURL(image.originalImagePath);
            }

            console.log('All images uploaded successfully');
        } catch (error) {
            console.error('Error uploading images:', error);
            throw error;
        }
    };

    // Handle cancel
    const handleCancel = () => {
        router.push("/admin/products");
    };

    return (
        <div className="space-y-6">
            <Tabs defaultValue="details">
                {/* Tabs Navigation - Outside of Card */}
                <div className="overflow-x-auto">
                    <TabsList className="inline-flex h-auto p-1 bg-muted rounded-lg min-w-max">
                        <TabsTrigger value="details" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <FileText className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.details")}</span>
                        </TabsTrigger>
                        <TabsTrigger value="attributes" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <Settings className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.attributes")}</span>
                        </TabsTrigger>
                        {/* Varyantlar sekmesi sadece varyantlı ürünlerde görünür */}
                        {formData.productType === 1 && (
                            <TabsTrigger value="variants" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                                <Tag className="h-4 w-4" />
                                <span className="whitespace-nowrap">{t("tabs.variants")}</span>
                            </TabsTrigger>
                        )}
                        <TabsTrigger value="seo" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <BarChart className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.seo")}</span>
                        </TabsTrigger>
                        <TabsTrigger value="faq" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <HelpCircle className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.faq")}</span>
                        </TabsTrigger>
                    </TabsList>
                </div>

                {/* Tab Content - Inside Card */}
                <Card>
                    <CardContent className="p-6">
                        <TabsContent value="details" className="space-y-6 mt-0">
                            <ProductAddDetailsTab formData={formData} updateFormData={updateFormData} />
                        </TabsContent>

                        <TabsContent value="attributes" className="mt-0">
                            <ProductAddAttributesTab categoryId={formData.categoryId} attributeMappings={formData.attributeMappings} updateAttributeMappings={updateAttributeMappings} productType={formData.productType} />
                        </TabsContent>

                        {/* Varyantlar tab content sadece varyantlı ürünlerde görünür */}
                        {formData.productType === 1 && (
                            <TabsContent value="variants" className="mt-0">
                                <ProductAddVariantsTab
                                    variants={formData.variants}
                                    updateVariants={(variants: ProductVariant[]) => updateFormData({ variants })}
                                    isProductSaved={false}
                                    attributeMappings={formData.attributeMappings}
                                    basePrice={formData.price}
                                    baseSku={formData.sku}
                                    categoryId={formData.categoryId}
                                />
                            </TabsContent>
                        )}

                        <TabsContent value="seo" className="space-y-4 mt-0">
                            <ProductAddSEOTab seo={formData.seo} updateSEO={(seo: ProductSEO) => updateFormData({ seo })} />
                        </TabsContent>

                        <TabsContent value="faq" className="space-y-4 mt-0">
                            <ProductAddFAQTab faq={formData.faq} updateFAQ={(faq: ProductFAQ[]) => updateFormData({ faq })} />
                        </TabsContent>

                        {/* Action Buttons */}
                        <div className="flex justify-between pt-6 border-t mt-6">
                            <Button variant="outline" onClick={handleCancel}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                {commonT("actions.cancel")}
                            </Button>
                            <Button onClick={handleSubmit} disabled={isLoading}>
                                <Save className="mr-2 h-4 w-4" />
                                {isLoading ? "Kaydediliyor..." : t("add")}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </Tabs>
        </div>
    );
}
