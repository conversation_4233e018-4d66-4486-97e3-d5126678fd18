'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { ProductListDto, ProductFilterParams } from '../types';
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronRight, Edit, Trash2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { PermissionGuard } from '@/components/auth/permission-guard';
import { api } from '@/lib/api/client';

interface ProductTableListClientProps {
  products: ProductListDto[];
  filters: ProductFilterParams;
  expandedProducts: string[];
  toggleProductExpansion: (productId: string) => void;
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function ProductTableListClient({ products, filters, expandedProducts, toggleProductExpansion, canRead, canCreate, canUpdate, canDelete }: ProductTableListClientProps) {
  const t = useTranslations("product");
  const commonT = useTranslations("common");
  const router = useRouter();

  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [deleteType, setDeleteType] = useState<"product" | "variant">("product");

  // Handle edit click
  const handleEditClick = (id: string, type: "product" | "variant") => {
    if (type === "product") {
      router.push(`/admin/products/edit/${id}`);
    } else {
      router.push(`/admin/products/edit/${id}`); // Variant edit will use same page
    }
  };

  // Handle delete click
  const handleDeleteClick = (id: string, type: "product" | "variant") => {
    setDeleteId(id);
    setDeleteType(type);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirm
  const handleDeleteConfirm = () => {
    if (!deleteId) return;

    api.delete(`product/${deleteId}`).then(() => {
      router.refresh();
    })
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  // Filter products based on the filters passed from parent
  const filteredProducts = products.filter((product) => {
    // Filter by search term (name, category, description)
    if (filters.search && !product.name.toLowerCase().includes(filters.search.toLowerCase()) && !product.category?.name?.toLowerCase().includes(filters.search.toLowerCase()) && !product.description?.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    // Filter by min stock
    if (filters.minStock !== undefined && (product.stockQuantity === undefined || product.stockQuantity < filters.minStock)) {
      return false;
    }

    // Filter by max stock
    if (filters.maxStock !== undefined && (product.stockQuantity === undefined || product.stockQuantity > filters.maxStock)) {
      return false;
    }

    return true;
  });

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">{t("fields.name")}</TableHead>
            <TableHead>{t("fields.category")}</TableHead>
            <TableHead>{t("fields.price")}</TableHead>
            <TableHead>{t("fields.discountedPrice")}</TableHead>
            <TableHead>{t("fields.stock")}</TableHead>
            <TableHead>{t("fields.variants")}</TableHead>
            <TableHead className="text-right">{commonT("actions.title")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredProducts.map((product) => (
            <React.Fragment key={product.id}>
              <TableRow>
                <TableCell>
                  <div className="flex items-center">
                    {product.variants && product.variants.length > 0 ? (
                      <Button variant="ghost" size="sm" className="p-0 h-6 w-6 mr-2" onClick={() => toggleProductExpansion(product.id)}>
                        {expandedProducts.includes(product.id) ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </Button>
                    ) : (
                      <div className="w-6 mr-2" />
                    )}
                    <span className="font-medium">{product.name}</span>
                  </div>
                </TableCell>
                <TableCell>{product.category?.name || "-"}</TableCell>
                <TableCell>{product.price?.toFixed(2)}</TableCell>
                <TableCell>{product.discountedPrice?.toFixed(2)}</TableCell>
                <TableCell>{product.stockQuantity}</TableCell>
                <TableCell>{product.variantCount || 0}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end space-x-1">
                    {canUpdate && (
                      <Button variant="ghost" size="sm" onClick={() => handleEditClick(product.id, "product")}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                    {canDelete && (
                      <Button variant="ghost" size="sm" className="text-red-600" onClick={() => handleDeleteClick(product.id, "product")}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>

              {/* Variants */}
              {product.variants &&
                product.variants.length > 0 &&
                expandedProducts.includes(product.id) &&
                product.variants.map((variant) => (
                  <TableRow key={variant.id} className="bg-muted/50">
                    <TableCell>
                      <div className="flex items-center">
                        <div className="w-6 mr-2" />
                        <span className="ml-6">└─ {variant.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{variant.category?.name || "-"}</TableCell>
                    <TableCell>{variant.price?.toFixed(2)}</TableCell>
                    <TableCell>{variant.discountedPrice?.toFixed(2)}</TableCell>
                    <TableCell>{variant.stockQuantity}</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-1">
                        {canUpdate && (
                          <Button variant="ghost" size="sm" onClick={() => handleEditClick(variant.id, "variant")}>
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                        {canDelete && (
                          <Button variant="ghost" size="sm" className="text-red-600" onClick={() => handleDeleteClick(variant.id, "variant")}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </React.Fragment>
          ))}

          {filteredProducts.length === 0 && (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-6">
                {t("noProducts")}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Delete confirmation dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{deleteType === "product" ? "Ürünü Sil" : "Varyantı Sil"}</DialogTitle>
            <DialogDescription>{deleteType === "product" ? "Bu ürünü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz." : "Bu varyantı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setDeleteDialogOpen(false)}>{commonT("actions.cancel")}</Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              {commonT("actions.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
