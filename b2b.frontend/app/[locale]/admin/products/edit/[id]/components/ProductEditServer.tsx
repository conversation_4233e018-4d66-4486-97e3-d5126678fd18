import { notFound } from "next/navigation";
import {
  getProduct,
  getProductBrands,
  getProductCategories,
  getProductAttributes
} from "@/lib/api/server/products";
import ProductEditClient from "./ProductEditClient";

interface ProductEditServerProps {
  productId: string;
}

export default async function ProductEditServer({ productId }: ProductEditServerProps) {
  try {
    // Fetch all required data in parallel
    const [product, brands, categories, attributes] = await Promise.all([
      getProduct(productId),
      getProductBrands(),
      getProductCategories(),
      getProductAttributes()
    ]);

    if (!product) {
      notFound();
    }
    console.log(product)
    return (
      <ProductEditClient
        product={product}
        brands={brands}
        categories={categories}
        attributes={attributes}
      />
    );
  } catch (error) {
    console.error("Error fetching product data:", error);
    notFound();
  }
}
