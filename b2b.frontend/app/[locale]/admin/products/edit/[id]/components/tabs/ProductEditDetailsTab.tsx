"use client"

import React, { useState, useRef } from "react";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Upload, X, Star } from "lucide-react";
import { Combobox } from "@/components/ui/combobox";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import type { ProductFormData, ProductImage } from "../../../../components/ProductAddForm";
import { useCategories } from "@/lib/api/hooks/useCategories";
import { useBrands } from "@/lib/api/hooks/useBrands";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useDeleteProductImage, useSetMainProductImage } from "@/lib/api/hooks/useProductImages";

// Özel Input bileşeni - border olmayan, alt çizgili
const BorderlessInput = ({ className, ...props }: React.ComponentProps<typeof Input>) => {
    return <Input className={cn("border-0 border-b border-gray-200 rounded-none px-0 focus-visible:ring-0 focus-visible:border-primary", className)} {...props} />;
};

interface ProductEditDetailsTabProps {
    productId: string;
    productSlug: string;
    formData: ProductFormData;
    updateFormData: (updates: Partial<ProductFormData>) => void;
}

export function ProductEditDetailsTab({ productId: _productId, productSlug: _productSlug, formData, updateFormData }: ProductEditDetailsTabProps) {
    const t = useTranslations("product");
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [deletingImageIndex, setDeletingImageIndex] = useState<number | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // API hooks
    const { data: categories, isLoading: categoriesLoading } = useCategories();
    const { data: brands, isLoading: brandsLoading } = useBrands();
    const deleteImageMutation = useDeleteProductImage();
    const setMainImageMutation = useSetMainProductImage();

    // Prepare category options for combobox
    const categoryOptions =
        categories?.map((category) => ({
            value: category.id,
            label: category.name,
        })) || [];

    // Prepare brand options for combobox
    const brandOptions =
        brands?.map((brand) => ({
            value: brand.id,
            label: brand.name,
        })) || [];

    // Handle image upload
    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            const newImages: ProductImage[] = Array.from(files).map((file, index) => ({
                id: crypto.randomUUID(),
                originalImagePath: URL.createObjectURL(file), // Temporary URL for preview
                thumbnailMediumPath: URL.createObjectURL(file), // Same for now
                thumbnailSmallPath: URL.createObjectURL(file),
                isMainImage: formData.images.length === 0 && index === 0, // First image is main
                sortOrder: formData.images.length + index + 1,
                altText: formData.name || "Product image",
            }));
            updateFormData({ images: [...formData.images, ...newImages] });
        }
    };

    // Remove image
    const removeImage = async (index: number) => {
        const imageToRemove = formData.images[index];

        // Set loading state
        setDeletingImageIndex(index);

        try {
            // Check if this is an existing image (has ID and not a blob URL) or a new image (blob URL)
            const isExistingImage = imageToRemove.id && !imageToRemove.originalImagePath.startsWith("blob:");

            if (isExistingImage) {
                // Delete from backend first
                await deleteImageMutation.mutateAsync(imageToRemove.id);
                console.log(`Image ${imageToRemove.id} deleted from backend`);
            }

            // Remove from form state
            const newImages = formData.images.filter((_, i) => i !== index);
            // Reorder sort orders
            const reorderedImages = newImages.map((img, i) => ({
                ...img,
                sortOrder: i + 1,
                isMainImage: i === 0, // First image becomes main
            }));
            updateFormData({ images: reorderedImages });

            // Update current image index
            if (currentImageIndex >= reorderedImages.length && reorderedImages.length > 0) {
                setCurrentImageIndex(reorderedImages.length - 1);
            } else if (reorderedImages.length === 0) {
                setCurrentImageIndex(0);
            }
        } catch (error) {
            console.error("Error deleting image:", error);
        } finally {
            // Clear loading state
            setDeletingImageIndex(null);
        }
    };

    // Set main image
    const setMainImage = async (index: number) => {
        const imageToSetAsMain = formData.images[index];

        // Check if this is an existing image (has ID and not a blob URL)
        const isExistingImage = imageToSetAsMain.id && !imageToSetAsMain.originalImagePath.startsWith("blob:");

        if (isExistingImage && _productId) {
            try {
                // Update main image in backend
                await setMainImageMutation.mutateAsync({
                    productId: _productId,
                    imageId: imageToSetAsMain.id,
                });
                console.log(`Image ${imageToSetAsMain.id} set as main in backend`);
            } catch (error) {
                console.error("Error setting main image in backend:", error);
                // Don't proceed with UI update if backend update failed
                return;
            }
        }

        // Update form state
        const updatedImages = formData.images.map((img, i) => ({
            ...img,
            isMainImage: i === index,
        }));
        updateFormData({ images: updatedImages });
    };

    // Move image up in order
    const moveImageUp = (index: number) => {
        if (index === 0) return;
        const newImages = [...formData.images];
        [newImages[index - 1], newImages[index]] = [newImages[index], newImages[index - 1]];
        // Update sort orders
        const reorderedImages = newImages.map((img, i) => ({
            ...img,
            sortOrder: i + 1,
        }));
        updateFormData({ images: reorderedImages });
        setCurrentImageIndex(index - 1);
    };

    // Move image down in order
    const moveImageDown = (index: number) => {
        if (index === formData.images.length - 1) return;
        const newImages = [...formData.images];
        [newImages[index], newImages[index + 1]] = [newImages[index + 1], newImages[index]];
        // Update sort orders
        const reorderedImages = newImages.map((img, i) => ({
            ...img,
            sortOrder: i + 1,
        }));
        updateFormData({ images: reorderedImages });
        setCurrentImageIndex(index + 1);
    };

    return (
        <div className="space-y-6">
            {/* Ürün Görselleri ve Temel Bilgiler */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Sol Taraf - Ürün Görselleri */}
                <div>
                    <Label className="text-base font-medium mb-4 block">{t("form.productImages")}</Label>

                    {/* Ana resim görüntüleme alanı */}
                    <div className="space-y-4">
                        <div className="aspect-square relative rounded-lg overflow-hidden bg-gray-100 border-2 border-dashed border-gray-300 cursor-pointer hover:border-gray-400 transition-colors" onClick={() => fileInputRef.current?.click()}>
                            {formData.images.length > 0 ? (
                                <Image src={formData.images[currentImageIndex]?.originalImagePath || "/placeholder.svg"} alt={formData.images[currentImageIndex]?.altText || "Product image"} fill className="object-cover" />
                            ) : (
                                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                                    <Upload className="h-12 w-12 mb-2" />
                                    <p className="text-sm">Resim yüklemek için tıklayın</p>
                                </div>
                            )}
                        </div>

                        {/* Resim küçük resimleri ve kontrolleri */}
                        <div className="flex flex-wrap gap-2">
                            {formData.images.map((image, index) => (
                                <div key={image.id} className="relative group">
                                    <div className={`w-16 h-16 relative rounded-md overflow-hidden cursor-pointer border-2 ${index === currentImageIndex ? "border-primary" : "border-gray-200"} ${image.isMainImage ? "ring-2 ring-yellow-400" : ""}`} onClick={() => setCurrentImageIndex(index)}>
                                        <Image src={image.thumbnailMediumPath || image.originalImagePath} alt={image.altText} fill className="object-cover" />
                                        {image.isMainImage && (
                                            <div className="absolute top-0 right-0 bg-yellow-400 text-white p-1 rounded-bl-md">
                                                <Star className="h-3 w-3" />
                                            </div>
                                        )}
                                    </div>

                                    {/* Resim kontrolleri - hover'da görünür */}
                                    <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <Button
                                            size="sm"
                                            variant="destructive"
                                            className="h-6 w-6 p-0 rounded-full"
                                            disabled={deletingImageIndex === index}
                                            onClick={async (e) => {
                                                e.stopPropagation();
                                                await removeImage(index);
                                            }}
                                        >
                                            {deletingImageIndex === index ? <div className="h-3 w-3 border border-white border-t-transparent rounded-full animate-spin" /> : <X className="h-3 w-3" />}
                                        </Button>
                                    </div>
                                </div>
                            ))}

                            {/* Resim yükleme butonu */}
                            <div className="w-16 h-16 flex items-center justify-center rounded-md cursor-pointer bg-muted hover:bg-muted/80 border-2 border-dashed border-gray-300" onClick={() => fileInputRef.current?.click()}>
                                <Plus className="h-6 w-6 text-muted-foreground" />
                            </div>
                        </div>

                        {/* Gizli file input */}
                        <input ref={fileInputRef} type="file" multiple accept="image/*" onChange={handleImageUpload} className="hidden" />

                        {/* Resim kontrol butonları */}
                        {formData.images.length > 0 && (
                            <div className="flex gap-2 flex-wrap">
                                <Button size="sm" variant="outline" onClick={async () => await setMainImage(currentImageIndex)} disabled={formData.images[currentImageIndex]?.isMainImage}>
                                    <Star className="h-4 w-4 mr-1" />
                                    Ana Resim Yap
                                </Button>
                                <Button size="sm" variant="outline" onClick={() => moveImageUp(currentImageIndex)} disabled={currentImageIndex === 0}>
                                    ↑ Yukarı
                                </Button>
                                <Button size="sm" variant="outline" onClick={() => moveImageDown(currentImageIndex)} disabled={currentImageIndex === formData.images.length - 1}>
                                    ↓ Aşağı
                                </Button>
                            </div>
                        )}
                    </div>
                </div>

                {/* Sağ Taraf - Temel Bilgiler */}
                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="product-name">{t("fields.name")} *</Label>
                        <BorderlessInput id="product-name" value={formData.name} onChange={(e) => updateFormData({ name: e.target.value })} placeholder="Ürün adını girin" required />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="product-sku">{t("fields.sku")} *</Label>
                        <BorderlessInput id="product-sku" value={formData.sku} onChange={(e) => updateFormData({ sku: e.target.value })} placeholder="Stok kodunu girin" required />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="product-barcode">{t("fields.barcode")}</Label>
                        <BorderlessInput id="product-barcode" value={formData.barcode} onChange={(e) => updateFormData({ barcode: e.target.value })} placeholder="Barkod numarasını girin" />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="product-category">{t("fields.category")} *</Label>
                        <Combobox
                            options={categoryOptions}
                            value={formData.categoryId}
                            onValueChange={(value) => updateFormData({ categoryId: Array.isArray(value) ? value[0] : value })}
                            placeholder={categoriesLoading ? "Kategoriler yükleniyor..." : t("combobox.selectCategory")}
                            className="border-0 border-b border-gray-200 rounded-none focus-within:border-primary"
                            disabled={categoriesLoading}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="product-brand">{t("fields.brand")}</Label>
                        <Combobox
                            options={brandOptions}
                            value={formData.brandId}
                            onValueChange={(value) => updateFormData({ brandId: Array.isArray(value) ? value[0] : value })}
                            placeholder={brandsLoading ? "Markalar yükleniyor..." : t("combobox.selectBrand")}
                            className="border-0 border-b border-gray-200 rounded-none focus-within:border-primary"
                            disabled={brandsLoading}
                        />
                    </div>
                    <div className="flex gap-x-8">
                        <div className="space-y-2 w-1/2">
                            <Label htmlFor="product-type">{t("fields.productType")} *</Label>
                            <Select value={formData.productType.toString()} onValueChange={(value) => updateFormData({ productType: Number(value) })}>
                                <SelectTrigger className="border-0 border-b border-gray-200 rounded-none px-0 focus:ring-0 w-full">
                                    <SelectValue placeholder="Ürün tipini seçin" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="0">{t("productType.Simple")}</SelectItem>
                                    <SelectItem value="1">{t("productType.Variant")}</SelectItem>
                                    <SelectItem value="2">{t("productType.Grouped")}</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2 w-1/2">
                            <Label htmlFor="product-status">{t("fields.status")}</Label>
                            <Select value={formData.status} onValueChange={(value) => updateFormData({ status: value })}>
                                <SelectTrigger className="border-0 border-b border-gray-200 rounded-none px-0 focus:ring-0 w-full">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="active">{t("status.active")}</SelectItem>
                                    <SelectItem value="inactive">{t("status.inactive")}</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </div>
            </div>

            <Separator />

            {/* Açıklama */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium">{t("description")}</h3>
                <Textarea value={formData.description} onChange={(e) => updateFormData({ description: e.target.value })} placeholder="Ürün açıklamasını girin" rows={4} className="resize-none border-0 border-b border-gray-200 rounded-none px-0 focus-visible:ring-0 focus-visible:border-primary" />
            </div>

            <Separator />

            {/* Fiyat ve Stok */}
            <div className="space-y-4">
                <h3 className="text-lg font-medium">{t("form.priceAndStock")}</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="product-price">{t("form.normalPrice")} *</Label>
                        <BorderlessInput id="product-price" type="number" step="0.01" min="0" value={formData.price || ""} onChange={(e) => updateFormData({ price: Number(e.target.value) })} placeholder="0.00" required />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="product-sale-price">{t("form.discountPrice")}</Label>
                        <BorderlessInput id="product-sale-price" type="number" step="0.01" min="0" value={formData.salePrice || ""} onChange={(e) => updateFormData({ salePrice: e.target.value ? Number(e.target.value) : undefined })} placeholder="0.00" />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="product-stock">{t("fields.stock")} *</Label>
                        <BorderlessInput id="product-stock" type="number" min="0" value={formData.stock || ""} onChange={(e) => updateFormData({ stock: Number(e.target.value) })} placeholder="0" required />
                    </div>
                </div>
            </div>

            <Separator />

            {/* Durum
          TODO: PublishDate eklenecek. Zamanlanmış ürün çıkartma özelliği olacak.
          */}
            {/* <div className="space-y-4">
              <h3 className="text-lg font-medium">{t("form.statusAndDates")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4"></div>
          </div> */}
        </div>
    );
}
