"use client"

import type React from "react"
import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Tag,
    BarChart,
    Settings,
    FileText,
    HelpCircle,
    Save,
    ArrowLeft,
} from "lucide-react"
import { useTranslations } from "next-intl"
import { useRouter } from "next/navigation"
import { useUpdateProduct, type ProductUpdateDto, type ProductDto } from "@/lib/api/hooks/useProducts"
import { type ProductBrandDto } from "@/types/brand"
import { type CategoryDto } from "@/lib/api/hooks/useProductCategories"
import { type ProductAttributeDto } from "@/app/[locale]/admin/product-attributes/types"
// Legacy imports removed - now using MediaAPI integration

// Import tab components
import { ProductEditDetailsTab } from "./tabs/ProductEditDetailsTab";
import { ProductAddAttributesTab } from "../../../components/tabs/ProductAddAttributesTab";
import { ProductAddVariantsTab } from "../../../components/tabs/ProductAddVariantsTab";
import { ProductAddSEOTab } from "../../../components/tabs/ProductAddSEOTab";
import { ProductAddFAQTab } from "../../../components/tabs/ProductAddFAQTab";
import type { ProductAttributeMappingCreateDto } from "@/lib/api/hooks/useProductAttributeMappings";

// Import types from ProductAddForm
import type { ProductFormData, ProductVariant, ProductFAQ, ProductSEO } from "../../../components/ProductAddForm";

interface ProductEditClientProps {
    product: ProductDto;
    brands: ProductBrandDto[];
    categories: CategoryDto[];
    attributes: ProductAttributeDto[];
}

export default function ProductEditClient({ product, brands: _brands, categories: _categories, attributes: _attributes }: ProductEditClientProps) {
    const t = useTranslations("product");
    const commonT = useTranslations("common");
    const router = useRouter();
    const updateProductMutation = useUpdateProduct();

    // Convert ProductDto to ProductFormData
    const convertProductToFormData = (product: ProductDto): ProductFormData => {
        // Debug: Gelen product verisini console'a yazdır
        console.log("ProductEditClient - Product Data:", product);
        console.log("ProductEditClient - Product SEO:", product.seo);
        return {
            name: product.name || "",
            sku: product.sku || "",
            barcode: product.barcode || "",
            description: product.description || "",
            productType: product.productType || 0,
            categoryId: product.category?.id || "",
            brandId: product.brand?.id || "",
            price: product.price || 0,
            salePrice: undefined, // TODO: Add discounted price support
            stock: product.stockQuantity || 0,
            status: product.isActive ? "active" : "inactive",
            images:
                product.images?.map((img) => ({
                    id: img.id,
                    originalImagePath: img.originalImagePath || "",
                    thumbnailMediumPath: img.thumbnailMediumPath || "",
                    thumbnailSmallPath: img.thumbnailSmallPath || "",
                    isMainImage: img.isMainImage,
                    sortOrder: img.sortOrder,
                    altText: img.altText || "",
                })) || [],
            attributeMappings:
                product.attributeMappings?.map((mapping) => ({
                    attributeId: mapping.attributeId,
                    attributeValueId: mapping.attributeValueId,
                })) || [],
            attributes: [], // Legacy field
            variants:
                product.variants?.map((variant) => ({
                    id: variant.id,
                    variantTypeId: undefined,
                    variantTypeName: variant.name,
                    optionId: undefined,
                    optionValue: variant.name,
                    sku: variant.sku || "",
                    barcode: variant.barcode || "",
                    stock: variant.stockQuantity || 0,
                    price: variant.price || 0,
                    salePrice: undefined,
                    isActive: variant.isActive,
                    isDeleted: variant.isDeleted,
                    attributeCombination:
                        variant.attributeMappings?.map((mapping) => ({
                            attributeId: mapping.attributeId,
                            attributeValueId: mapping.attributeValueId,
                            attributeName: mapping.attribute?.name || "",
                            valueName: mapping.attributeValue?.value || "",
                        })) || [],
                })) || [],
            seo: product.seo
                ? {
                    metaTitle: product.seo.metaTitle || "",
                    metaDescription: product.seo.metaDescription || "",
                    metaKeywords: product.seo.metaKeywords || "",
                    ogTitle: product.seo.ogTitle || "",
                    ogDescription: product.seo.ogDescription || "",
                    ogImage: product.seo.ogImage || "",
                    structuredData: product.seo.structuredData || "",
                    canonicalUrl: product.seo.canonicalUrl || "",
                    noIndex: product.seo.noIndex || false,
                    noFollow: product.seo.noFollow || false,
                }
                : {
                    metaTitle: "",
                    metaDescription: "",
                    metaKeywords: "",
                    ogTitle: "",
                    ogDescription: "",
                    ogImage: "",
                    structuredData: "",
                    canonicalUrl: "",
                    noIndex: false,
                    noFollow: false,
                },
            faq:
                product.faqs?.map((faq) => ({
                    id: faq.id,
                    question: faq.question,
                    answer: faq.answer,
                })) || [],
        };
    };

    // Form state - initialize with product data
    const [formData, setFormData] = useState<ProductFormData>(() => convertProductToFormData(product));

    const [isLoading, setIsLoading] = useState(false);

    // Update form data when product changes
    useEffect(() => {
        setFormData(convertProductToFormData(product));
    }, [product]);

    // Update form data
    const updateFormData = useCallback((updates: Partial<ProductFormData>) => {
        setFormData((prev) => ({ ...prev, ...updates }));
    }, []);

    // Memoized update functions
    const updateAttributeMappings = useCallback(
        (attributeMappings: ProductAttributeMappingCreateDto[]) => {
            updateFormData({ attributeMappings });
        },
        [updateFormData]
    );

    // Handle form submission
    const handleSubmit = async () => {
        console.log('🎯 handleSubmit called');
        console.log('📊 Form data images:', formData.images.map(img => ({
            id: img.id,
            originalImagePath: img.originalImagePath.substring(0, 50) + '...',
            isBlob: img.originalImagePath.startsWith('blob:'),
            sortOrder: img.sortOrder
        })));

        setIsLoading(true);
        try {
            // Prepare product data for API - backend'deki ProductUpdateDto ile uyumlu
            const productData: ProductUpdateDto = {
                id: product.id,
                name: formData.name,
                description: formData.description || undefined,
                productType: formData.productType,
                categoryId: formData.categoryId || undefined,
                brandId: formData.brandId || undefined,
                sku: formData.sku || undefined,
                barcode: formData.barcode || undefined,
                price: formData.price || undefined,
                stockQuantity: formData.stock || undefined,
                isActive: formData.status === "active",
                // Related data
                attributeMappings:
                    formData.attributeMappings.length > 0
                        ? formData.attributeMappings.map((mapping) => ({
                            attributeId: mapping.attributeId,
                            attributeValueId: mapping.attributeValueId,
                        }))
                        : undefined,

                faqs:
                    formData.faq.length > 0
                        ? formData.faq.map((faq, index) => ({
                            question: faq.question,
                            answer: faq.answer,
                            sortOrder: index + 1,
                        }))
                        : undefined,
                seo: formData.seo.metaTitle || formData.seo.metaDescription || formData.seo.metaKeywords ? formData.seo : undefined,
                variants:
                    formData.variants.length > 0
                        ? formData.variants.map((variant) => ({
                            name: variant.variantTypeName,
                            sku: variant.sku,
                            barcode: variant.barcode || undefined,
                            price: variant.price,
                            stockQuantity: variant.stock,
                            isActive: variant.isActive,
                            attributeMappings:
                                variant.attributeCombination?.map((attr) => ({
                                    attributeId: attr.attributeId,
                                    attributeValueId: attr.attributeValueId,
                                })) || [],
                        }))
                        : undefined,
            };

            console.log('💾 Updating product...');
            // 1. First update the product
            await updateProductMutation.mutateAsync(productData);
            console.log('✅ Product updated successfully');

            // 2. Then handle image uploads if there are new images (with blob URLs)
            const newImages = formData.images.filter((img) => img.originalImagePath.startsWith("blob:"));
            console.log('🔍 Filtering new images:', {
                totalImages: formData.images.length,
                newImagesCount: newImages.length,
                newImages: newImages.map(img => ({
                    id: img.id,
                    originalImagePath: img.originalImagePath.substring(0, 50) + '...',
                    sortOrder: img.sortOrder
                }))
            });

            if (newImages.length > 0) {
                console.log('📤 Starting image upload process...');
                await handleImageUploads(newImages, product.slug || product.name.toLowerCase().replace(/\s+/g, "-"));
                console.log('✅ Image upload process completed');
            } else {
                console.log('⚠️ No new images to upload');
            }

            console.log('🔄 Redirecting to products list...');
            // Redirect to products list
            router.push("/admin/products");
        } catch (error) {
            console.error("Error updating product:", error);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle image uploads - Now using MediaAPI
    const handleImageUploads = async (newImages: typeof formData.images, productSlug: string) => {
        console.log('🚀 handleImageUploads called with:', {
            newImagesCount: newImages.length,
            productSlug,
            productId: product.id,
            newImages: newImages.map(img => ({
                id: img.id,
                originalImagePath: img.originalImagePath.substring(0, 50) + '...',
                isBlob: img.originalImagePath.startsWith('blob:'),
                sortOrder: img.sortOrder,
                altText: img.altText,
                isMainImage: img.isMainImage
            }))
        });

        if (newImages.length === 0) {
            console.log('⚠️ No new images to upload');
            return;
        }

        try {
            console.log('📦 Importing upload utilities...');
            // Import the upload utility
            const { uploadImageToMediaAPI } = await import('@/lib/utils/fileUtils');
            const { prepareImageUpload } = await import('@/lib/utils/imageUtils');
            console.log('✅ Upload utilities imported successfully');

            for (let i = 0; i < newImages.length; i++) {
                const image = newImages[i];
                console.log(`🖼️ Processing image ${i + 1}/${newImages.length}:`, {
                    imageId: image.id,
                    originalImagePath: image.originalImagePath.substring(0, 50) + '...',
                    sortOrder: image.sortOrder,
                    altText: image.altText,
                    isMainImage: image.isMainImage
                });

                console.log('📥 Fetching blob from URL...');
                // Get the file from the blob URL
                const response = await fetch(image.originalImagePath);
                const blob = await response.blob();
                console.log('✅ Blob fetched:', {
                    size: blob.size,
                    type: blob.type
                });

                const file = new File([blob], `${productSlug}-${image.sortOrder}.jpg`, { type: blob.type });
                console.log('📄 File created:', {
                    name: file.name,
                    size: file.size,
                    type: file.type
                });

                console.log('🔧 Preparing upload data...');
                // Prepare upload data
                const uploadData = prepareImageUpload(
                    file,
                    productSlug,
                    image.sortOrder,
                    image.altText,
                    image.isMainImage
                );
                console.log('✅ Upload data prepared:', {
                    fileName: uploadData.fileName,
                    sortOrder: uploadData.sortOrder,
                    altText: uploadData.altText,
                    isMainImage: uploadData.isMainImage
                });

                console.log('🚀 Calling uploadImageToMediaAPI...');
                // Upload to MediaAPI
                const result = await uploadImageToMediaAPI(product.id, uploadData);
                console.log('📨 Upload result:', result);

                if (result.success) {
                    console.log(`✅ Image ${i + 1} uploaded successfully:`, result.imageId);
                } else {
                    console.error(`❌ Failed to upload image ${i + 1}:`, result.message);
                    throw new Error(result.message || 'Image upload failed');
                }

                // Clean up blob URL
                URL.revokeObjectURL(image.originalImagePath);
                console.log('🧹 Blob URL cleaned up');
            }

            console.log('🎉 All images uploaded successfully');
        } catch (error) {
            console.error('💥 Error uploading images:', error);
            throw error;
        }
    };

    // Handle cancel
    const handleCancel = () => {
        router.push("/admin/products");
    };

    return (
        <div className="space-y-6">
            <Tabs defaultValue="details">
                {/* Tabs Navigation - Outside of Card */}
                <div className="overflow-x-auto">
                    <TabsList className="inline-flex h-auto p-1 bg-muted rounded-lg min-w-max">
                        <TabsTrigger value="details" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <FileText className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.details")}</span>
                        </TabsTrigger>
                        <TabsTrigger value="attributes" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <Settings className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.attributes")}</span>
                        </TabsTrigger>
                        {/* Varyantlar sekmesi sadece varyantlı ürünlerde görünür */}
                        {formData.productType === 1 && (
                            <TabsTrigger value="variants" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                                <Tag className="h-4 w-4" />
                                <span className="whitespace-nowrap">{t("tabs.variants")}</span>
                            </TabsTrigger>
                        )}
                        <TabsTrigger value="seo" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <BarChart className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.seo")}</span>
                        </TabsTrigger>
                        <TabsTrigger value="faq" className="flex items-center gap-2 px-4 py-2 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm">
                            <HelpCircle className="h-4 w-4" />
                            <span className="whitespace-nowrap">{t("tabs.faq")}</span>
                        </TabsTrigger>
                    </TabsList>
                </div>

                {/* Tab Content - Inside Card */}
                <Card>
                    <CardContent className="p-6">
                        <TabsContent value="details" className="space-y-6 mt-0">
                            <ProductEditDetailsTab productId={product.id} productSlug={product.slug || product.name.toLowerCase().replace(/\s+/g, "-")} formData={formData} updateFormData={updateFormData} />
                        </TabsContent>

                        <TabsContent value="attributes" className="mt-0">
                            <ProductAddAttributesTab categoryId={formData.categoryId} attributeMappings={formData.attributeMappings} updateAttributeMappings={updateAttributeMappings} productType={formData.productType} />
                        </TabsContent>

                        {/* Varyantlar tab content sadece varyantlı ürünlerde görünür */}
                        {formData.productType === 1 && (
                            <TabsContent value="variants" className="mt-0">
                                <ProductAddVariantsTab
                                    variants={formData.variants}
                                    updateVariants={(variants: ProductVariant[]) => updateFormData({ variants })}
                                    isProductSaved={true} // Product is already saved, so variants can be created
                                    attributeMappings={formData.attributeMappings}
                                    basePrice={formData.price}
                                    baseSku={formData.sku}
                                    categoryId={formData.categoryId}
                                />
                            </TabsContent>
                        )}

                        <TabsContent value="seo" className="space-y-4 mt-0">
                            <ProductAddSEOTab seo={formData.seo} updateSEO={(seo: ProductSEO) => updateFormData({ seo })} />
                        </TabsContent>

                        <TabsContent value="faq" className="space-y-4 mt-0">
                            <ProductAddFAQTab faq={formData.faq} updateFAQ={(faq: ProductFAQ[]) => updateFormData({ faq })} />
                        </TabsContent>

                        {/* Action Buttons */}
                        <div className="flex justify-between pt-6 border-t mt-6">
                            <Button variant="outline" onClick={handleCancel}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                {commonT("actions.cancel")}
                            </Button>
                            <Button onClick={handleSubmit} disabled={isLoading}>
                                <Save className="mr-2 h-4 w-4" />
                                {isLoading ? "Güncelleniyor..." : commonT("actions.save")}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </Tabs>
        </div>
    );
}
