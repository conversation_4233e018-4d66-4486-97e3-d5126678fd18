import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { hasServerPermission } from "@/lib/auth/server-permissions";
import { PermissionProvider } from "@/components/auth/permission-provider";
import ProductAttributeListServer from "./components/ProductAttributeListServer";
import PageHeaderServer from "../components/PageHeaderServer";

export default async function ProductAttributesPage() {
  // Server-side permission check - redirect if no access
  const canRead = await hasServerPermission("product_attributes", "read");
  if (!canRead) {
    redirect("/admin/dashboard");
  }

  const t = await getTranslations("productAttribute");
  return (
    <div className="space-y-6">
      <PermissionProvider resource="product_attributes">
        {({ read, create, update, delete: canDelete }) => (
          <>
            <PageHeaderServer
              title={t("title")}
              description={t("list")}
              actions={[
                {
                  actionLabel: t("add"),
                  actionUrl: "/admin/product-attributes/add",
                  actionIcon: "add",
                  actionVariant: "default",
                  permissionResource: "product_attributes",
                  permissionAction: "create",
                },
                {
                  actionLabel: t("productList"),
                  actionUrl: "/admin/products",
                  actionIcon: "list",
                  actionVariant: "link"
                }
              ]}
            />
            <ProductAttributeListServer canRead={read} canCreate={create} canUpdate={update} canDelete={canDelete} />
          </>
        )}
      </PermissionProvider>
    </div>
  );
}
