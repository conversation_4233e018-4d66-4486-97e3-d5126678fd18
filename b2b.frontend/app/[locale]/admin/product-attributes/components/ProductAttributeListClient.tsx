"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { ProductAttributeDto, ProductAttributeFilterParams } from "../types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { PlusCircle } from "lucide-react";
import AttributeItem from "./AttributeItem";
import AttributeModal from "./AttributeModal";
import { CategoryDto } from "../../product-categories/types";

interface ProductAttributeListClientProps {
  attributes: ProductAttributeDto[];
  categories: CategoryDto[];
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

export default function ProductAttributeListClient({ attributes, categories, canRead, canCreate, canUpdate, canDelete }: ProductAttributeListClientProps) {
  const t = useTranslations("productAttribute");
  const commonT = useTranslations("common");

  // State for expanded attributes
  const [expandedAttributes, setExpandedAttributes] = useState<string[]>([]);

  // State for attribute modal
  const [attributeModalOpen, setAttributeModalOpen] = useState(false);

  // State for filters
  const [filters, setFilters] = useState<ProductAttributeFilterParams>({
    search: "",
  });

  // Toggle attribute expansion
  const toggleAttributeExpansion = (attributeId: string) => {
    setExpandedAttributes((prev) => (prev.includes(attributeId) ? prev.filter((id) => id !== attributeId) : [...prev, attributeId]));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof ProductAttributeFilterParams, value: string | undefined) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      search: "",
    });
  };

  // Filtreleme işlemi
  const filtered = attributes.filter((attr) => !filters.search || attr.name.toLowerCase().includes(filters.search.toLowerCase()));

  return (
    <div className="space-y-4">
      {/* Filter section */}
      <div className="bg-card p-4 rounded-md border">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">{commonT("filters.title")}</h3>

        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium mb-1 block">{t("name")}</label>
            <Input placeholder={t("searchPlaceholder")} value={filters.search || ""} onChange={(e) => handleFilterChange("search", e.target.value)} />
          </div>
        </div>
        <div className="flex justify-end mt-4 gap-2">
          <Button variant="outline" onClick={resetFilters}>
            {t("clear")}
          </Button>
        </div>
      </div>

      {/* Attributes table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("name")}</TableHead>
              <TableHead>{t("shortName")}</TableHead>
              <TableHead>{t("isVariantAttribute")}</TableHead>
              <TableHead>{t("isListAttribute")}</TableHead>
              <TableHead className="text-right">{commonT("actions.title")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filtered.length > 0 ? (
              filtered.map((attr) => (
                <AttributeItem
                  key={attr.id}
                  attribute={attr}
                  expandedAttributes={expandedAttributes}
                  toggleAttributeExpansion={toggleAttributeExpansion}
                  searchTerm={filters.search}
                  categories={categories}
                  canRead={canRead}
                  canCreate={canCreate}
                  canUpdate={canUpdate}
                  canDelete={canDelete}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6">
                  {t("noAttributes")}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* AttributeModal for adding new attributes */}
      <AttributeModal isOpen={attributeModalOpen} onClose={() => setAttributeModalOpen(false)} categories={categories} />
    </div>
  );
}
