import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import axios from 'axios';
import { redirect } from 'next/navigation'; // Next.js 13+ App Router için

// Server-side API client
const createServerApiClient = async () => {
  const session = await getServerSession(authOptions);

  const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:33800/admin-api',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Token varsa ekle
  if (session?.accessToken) {
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${session.accessToken}`;
  }

  return apiClient;
};
const redirectIfUnauthorized = async (status: number): Promise<void> => {
  if (status === 401)
    redirect('/');
}
// Server-side API functions
export const serverApi = {
  get: async <T>(url: string, params?: unknown): Promise<T | undefined> => {
    const client = await createServerApiClient();
    try {
      const response = await client.get<T>(url, { params });
      return response.data;

    }
    catch (error: any) {
      redirectIfUnauthorized(error.response.status);
    }
  },

  post: async <T>(url: string, data?: unknown): Promise<T> => {
    const client = await createServerApiClient();
    const response = await client.post<T>(url, data);
    return response.data;
  },

  put: async <T>(url: string, data?: unknown): Promise<T> => {
    const client = await createServerApiClient();
    const response = await client.put<T>(url, data);
    return response.data;
  },

  patch: async <T>(url: string, data?: unknown): Promise<T> => {
    const client = await createServerApiClient();
    const response = await client.patch<T>(url, data);
    return response.data;
  },

  delete: async <T>(url: string): Promise<T> => {
    const client = await createServerApiClient();
    const response = await client.delete<T>(url);
    return response.data;
  },

  // File upload için özel metod
  postFormData: async <T>(url: string, formData: FormData): Promise<T> => {
    const client = await createServerApiClient();
    // FormData için Content-Type header'ını kaldır, axios otomatik ayarlayacak
    delete client.defaults.headers['Content-Type'];
    const response = await client.post<T>(url, formData);
    return response.data;
  },
};
