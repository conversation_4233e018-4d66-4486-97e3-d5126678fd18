{"productAttribute": {"title": "Product Attributes", "list": "View, add or edit product attributes.", "add": "Add Attribute", "addDescription": "Add a new product attribute.", "edit": "Edit Attribute", "editDescription": "Edit the product attribute.", "back": "Back", "name": "Attribute Name", "shortName": "Short Name", "isVariantAttribute": "<PERSON><PERSON><PERSON> Attribute", "isListAttribute": "Show in Product Features", "categories": "Categories", "categoriesInfo": "When categories are selected, the attribute will be only visible in those categories", "searchPlaceholder": "Search attribute...", "clear": "Clear", "yes": "Yes", "no": "No", "categoriesSelected": "Categories Selected", "selectCategories": "Select Categories", "searchCategories": "Search categories...", "deleteValueConfirmTitle": "You are about to delete a product attribute value", "deleteValueConfirmMessage": "Are you sure you want to delete this product attribute value?", "deleteAttributeConfirmTitle": "You are about to delete a product attribute", "deleteAttributeConfirmMessage": "Are you sure you want to delete this product attribute?", "editAttribute": "Edit Attribute", "addAttribute": "Add Attribute", "editAttributeValue": "Edit Attribute Value", "addAttributeValue": "Add Attribute Value", "value": "Value", "valuePlaceholder": "Enter attribute value...", "namePlaceholder": "Enter attribute name...", "shortNamePlaceholder": "Enter short name...", "noAttributeValues": "No attribute values found", "noAttributes": "No attributes found", "loading": "Loading...", "variantAttributeHelp": "Variant attributes are used to create product variants", "listAttributeHelp": "List attributes are shown in product features section", "noCategoriesFound": "No categories found", "productList": "Product List"}}