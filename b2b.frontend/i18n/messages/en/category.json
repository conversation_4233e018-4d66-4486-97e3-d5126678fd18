{"category": {"title": "Categories", "single": "Category", "details": "Category Details", "list": "Category List", "add": "Add Category", "edit": "Edit Category", "delete": "Delete Category", "back": "Back", "addDescription": "Add a new category", "editDescription": "Edit category information", "addSuccess": "Category added successfully", "updateSuccess": "Category updated successfully", "deleteSuccess": "Category deleted successfully", "addError": "Error adding category", "updateError": "Error updating category", "deleteError": "Error deleting category", "noCategories": "No categories found", "noParentCategory": "No parent category", "productList": "Product List", "fields": {"name": "Name", "description": "Description", "parent": "Parent Category", "parentCategory": "Parent Category", "subcategories": "Subcategories", "subcategoryCount": "Subcategory Count", "productCount": "Product Count", "slug": "Slug", "status": "Status"}, "placeholders": {"name": "Enter category name", "slug": "category-slug", "parentCategory": "Select parent category", "search": "Search category..."}, "noResults": "No results found", "validation": {"nameRequired": "Category name is required", "slugRequired": "Slug is required"}, "status": {"active": "Active", "inactive": "Inactive"}, "filters": {"title": "Filters", "search": "Search...", "clear": "Clear Filters"}}}