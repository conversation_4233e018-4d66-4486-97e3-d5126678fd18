{"category": {"title": "<PERSON><PERSON><PERSON>", "single": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "list": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "addDescription": "<PERSON><PERSON> bir kategori e<PERSON>", "editDescription": "Kategori bi<PERSON>", "addSuccess": "<PERSON><PERSON>i başar<PERSON><PERSON> eklendi", "updateSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "addError": "Kategori eklenir<PERSON> bir hata o<PERSON>", "updateError": "<PERSON><PERSON><PERSON> g<PERSON>llen<PERSON> bir hata o<PERSON>", "deleteError": "<PERSON><PERSON><PERSON> bir hata <PERSON>", "noCategories": "<PERSON><PERSON><PERSON> b<PERSON>", "noParentCategory": "Üst kategori yok", "productList": "<PERSON><PERSON><PERSON><PERSON>", "fields": {"name": "İsim", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "parent": "Üst Kategori", "parentCategory": "Üst Kategori", "subcategories": "<PERSON>", "subcategoryCount": "Alt Kategori Sayısı", "productCount": "<PERSON><PERSON><PERSON><PERSON>", "slug": "URL Kodu", "status": "Durum"}, "placeholders": {"name": "<PERSON><PERSON><PERSON> adı girin", "slug": "kategori-url-kodu", "parentCategory": "<PERSON>st kate<PERSON>i se<PERSON>", "search": "<PERSON><PERSON>i ara..."}, "noResults": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "validation": {"nameRequired": "Kategori adı gereklidir", "slugRequired": "URL kodu gereklidir"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>"}, "filters": {"title": "<PERSON><PERSON><PERSON><PERSON>", "search": "Ara...", "clear": "<PERSON><PERSON><PERSON><PERSON>"}}}